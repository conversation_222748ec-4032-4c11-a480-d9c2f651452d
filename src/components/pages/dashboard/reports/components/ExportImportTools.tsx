"use client";

import React, { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { DatePicker } from "@/components/ui/date-picker";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Progress } from "@/components/ui/progress";
import {
  Download,
  Upload,
  FileText,
  FileSpreadsheet,
  <PERSON><PERSON>mage,
  Printer,
  Share2,
  Calendar,
  Settings,
} from "lucide-react";
import * as XLSX from "xlsx-js-style";
import { toast } from "sonner";
import {
  getSalesReportDataWithFilters,
  getPurchaseReportDataWithFilters,
  getProductReportDataWithFilters,
  getCustomerReportData,
  getSupplierReportData,
} from "@/actions/reports/reports";
import { createProfessionalExcelReport } from "@/utils/excelTemplate";
import { createProductImportTemplate } from "@/utils/importTemplate";
import { importProducts } from "@/actions/import/products";

interface FilterState {
  dateRange: string;
  startDate?: Date;
  endDate?: Date;
  category?: string;
  supplier?: string;
  customer?: string;
  status?: string;
}

interface ExportImportToolsProps {
  filters: FilterState;
}

interface ExportConfig {
  reportType: "harian" | "bulanan" | "tahunan";
  sections: {
    penjualan: boolean;
    pembelian: boolean;
    produk: boolean;
    pelanggan: boolean;
    supplier: boolean;
  };
  format: "excel" | "csv" | "pdf";
  includeCharts: boolean;
  includeSummary: boolean;
  // Date/period selection
  selectedDate?: Date; // For daily reports
  selectedMonth?: number; // For monthly reports (0-11)
  selectedYear?: number; // For monthly and yearly reports
}

export const ExportImportTools: React.FC<ExportImportToolsProps> = ({
  filters,
}) => {
  const [isExporting, setIsExporting] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [importProgress, setImportProgress] = useState(0);
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [showImportDialog, setShowImportDialog] = useState(false);
  const [importSummary, setImportSummary] = useState<any>(null);
  const [exportConfig, setExportConfig] = useState<ExportConfig>({
    reportType: "bulanan",
    sections: {
      penjualan: true,
      pembelian: true,
      produk: true,
      pelanggan: true,
      supplier: true,
    },
    format: "excel",
    includeCharts: false,
    includeSummary: true,
    // Initialize with current date/period
    selectedDate: new Date(),
    selectedMonth: new Date().getMonth(),
    selectedYear: new Date().getFullYear(),
  });
  const fileInputRef = useRef<HTMLInputElement>(null);

  const generateAdvancedExport = async () => {
    setIsExporting(true);
    setExportProgress(0);

    try {
      let progressStep = 0;
      const totalSteps = Object.values(exportConfig.sections).filter(
        Boolean
      ).length;

      // Prepare report data structure
      const reportData: any = {
        reportType: exportConfig.reportType,
        filters: filters,
        summary: {
          period: getReportPeriodText(),
          generatedAt: new Date(),
        },
      };

      // Collect Sales Data
      if (exportConfig.sections.penjualan) {
        setExportProgress((++progressStep / totalSteps) * 80);
        const customDateRange = getCustomDateRange();
        const salesResult = await getSalesReportDataWithFilters({
          dateRange: getDateRangeForReportType(),
          startDate: customDateRange.startDate,
          endDate: customDateRange.endDate,
          category: filters.category,
          supplier: filters.supplier,
          customer: filters.customer,
          status: filters.status,
        });

        if (salesResult.success && salesResult.data) {
          // Pass the data directly without re-mapping to preserve the new structure
          reportData.sales = salesResult.data;

          // Update summary - use totalAmount for new structure, fallback to total for legacy
          reportData.summary.totalSales = reportData.sales.reduce(
            (sum: number, sale: any) =>
              sum + (sale.totalAmount || sale.total || 0),
            0
          );
        } else {
          reportData.sales = []; // Ensure it's an empty array, not undefined
        }
      }

      // Collect Purchase Data
      if (exportConfig.sections.pembelian) {
        setExportProgress((++progressStep / totalSteps) * 80);
        const customDateRange = getCustomDateRange();
        const purchaseResult = await getPurchaseReportDataWithFilters({
          dateRange: getDateRangeForReportType(),
          startDate: customDateRange.startDate,
          endDate: customDateRange.endDate,
          category: filters.category,
          supplier: filters.supplier,
          customer: filters.customer,
          status: filters.status,
        });

        if (purchaseResult.success && purchaseResult.data) {
          // Pass the data directly without re-mapping to preserve the new structure
          reportData.purchases = purchaseResult.data;

          // Update summary - use totalAmount for new structure, fallback to total for legacy
          reportData.summary.totalPurchases = reportData.purchases.reduce(
            (sum: number, purchase: any) =>
              sum + (purchase.totalAmount || purchase.total || 0),
            0
          );
        } else {
          reportData.purchases = []; // Ensure it's an empty array, not undefined
        }
      }

      // Collect Product Data
      if (exportConfig.sections.produk) {
        setExportProgress((++progressStep / totalSteps) * 80);
        const customDateRange = getCustomDateRange();
        const productResult = await getProductReportDataWithFilters({
          dateRange: getDateRangeForReportType(),
          startDate: customDateRange.startDate,
          endDate: customDateRange.endDate,
          category: filters.category,
          supplier: filters.supplier,
          customer: filters.customer,
          status: filters.status,
        });
        if (productResult.success && productResult.data) {
          // Pass the data directly without re-mapping to preserve the new structure
          reportData.products = productResult.data;

          // Update summary
          reportData.summary.totalProducts = reportData.products.length;
        }
      }

      // Collect Customer Data
      if (exportConfig.sections.pelanggan) {
        setExportProgress((++progressStep / totalSteps) * 80);
        const customerResult = await getCustomerReportData();
        if (customerResult.success && customerResult.data) {
          // Pass the data directly without re-mapping to preserve the new structure
          reportData.customers = customerResult.data;

          // Update summary
          reportData.summary.totalCustomers = reportData.customers.length;
        }
      }

      // Collect Supplier Data
      if (exportConfig.sections.supplier) {
        setExportProgress((++progressStep / totalSteps) * 80);
        const supplierResult = await getSupplierReportData();
        if (supplierResult.success && supplierResult.data) {
          // Pass the data directly without re-mapping to preserve the new structure
          reportData.suppliers = supplierResult.data;

          // Update summary
          reportData.summary.totalSuppliers = reportData.suppliers.length;
        }
      }

      setExportProgress(90);

      // Generate report based on selected format
      if (exportConfig.format === "excel") {
        // Generate professional Excel report using the new template system
        const workbook = createProfessionalExcelReport(reportData, {
          companyName: "Kasir Online",
          reportTitle: `Laporan ${exportConfig.reportType.charAt(0).toUpperCase() + exportConfig.reportType.slice(1)}`,
          includeCharts: exportConfig.includeCharts,
          includeSummary: exportConfig.includeSummary,
          autoFitColumns: true,
        });

        setExportProgress(100);

        const fileName = `laporan-${exportConfig.reportType}-${new Date().toISOString().split("T")[0]}.xlsx`;
        XLSX.writeFile(workbook, fileName);
      } else if (exportConfig.format === "csv") {
        // Generate CSV export
        let csvContent = "";

        if (reportData.sales && exportConfig.sections.penjualan) {
          csvContent += "=== DATA PENJUALAN ===\n";
          csvContent +=
            "ID,Tanggal,Pelanggan,Jumlah Item,Total,No. Transaksi\n";
          reportData.sales.forEach((sale: any) => {
            const saleDate = sale.saleDate || sale.date;
            const customerName =
              sale.customer?.name || sale.customer || "Pelanggan Umum";
            const itemCount = sale.items?.length || sale.items || 0;
            const totalAmount = sale.totalAmount || sale.total || 0;
            csvContent += `${sale.id},"${new Date(saleDate).toLocaleDateString("id-ID")}","${customerName}",${itemCount},${totalAmount},"${sale.transactionNumber || "-"}"\n`;
          });
          csvContent += "\n";
        }

        if (reportData.purchases && exportConfig.sections.pembelian) {
          csvContent += "=== DATA PEMBELIAN ===\n";
          csvContent += "ID,Tanggal,Supplier,Jumlah Item,Total,Ref. Invoice\n";
          reportData.purchases.forEach((purchase: any) => {
            const purchaseDate = purchase.purchaseDate || purchase.date;
            const supplierName =
              purchase.supplier?.name ||
              purchase.supplier ||
              "Tidak ada supplier";
            const itemCount = purchase.items?.length || purchase.items || 0;
            const totalAmount = purchase.totalAmount || purchase.total || 0;
            csvContent += `${purchase.id},"${new Date(purchaseDate).toLocaleDateString("id-ID")}","${supplierName}",${itemCount},${totalAmount},"${purchase.invoiceRef || "-"}"\n`;
          });
          csvContent += "\n";
        }

        if (reportData.products && exportConfig.sections.produk) {
          csvContent += "=== DATA PRODUK ===\n";
          csvContent +=
            "ID,Nama Produk,Deskripsi,SKU,Barcode,Kategori,Unit,Stok,Harga Beli,Harga Jual,Terjual,Pendapatan,Keuntungan\n";
          reportData.products.forEach((product: any) => {
            const categoryName =
              product.category?.name ||
              product.category ||
              "Tidak ada kategori";
            const description = product.description || "";
            csvContent += `${product.id},"${product.name}","${description}","${product.sku || "-"}","${product.barcode || "-"}","${categoryName}","${product.unit || "pcs"}",${product.stock},${product.cost || 0},${product.price || 0},${product.sold || 0},${product.revenue || 0},${product.profit || 0}\n`;
          });
          csvContent += "\n";
        }

        if (reportData.customers && exportConfig.sections.pelanggan) {
          csvContent += "=== DATA PELANGGAN ===\n";
          csvContent +=
            "ID,Nama Pelanggan,Nama Kontak,Email,Telepon,Alamat,NIK,NPWP,Catatan,Dibuat\n";
          reportData.customers.forEach((customer: any) => {
            const nik = customer.NIK || customer.nik || "-";
            const npwp = customer.NPWP || customer.npwp || "-";
            csvContent += `${customer.id},"${customer.name}","${customer.contactName || "-"}","${customer.email || "-"}","${customer.phone || "-"}","${customer.address || "-"}","${nik}","${npwp}","${customer.notes || "-"}","${new Date(customer.createdAt).toLocaleDateString("id-ID")}"\n`;
          });
          csvContent += "\n";
        }

        if (reportData.suppliers && exportConfig.sections.supplier) {
          csvContent += "=== DATA SUPPLIER ===\n";
          csvContent +=
            "ID,Nama Supplier,Nama Kontak,Email,Telepon,Alamat,Catatan,Dibuat\n";
          reportData.suppliers.forEach((supplier: any) => {
            csvContent += `${supplier.id},"${supplier.name}","${supplier.contactName || "-"}","${supplier.email || "-"}","${supplier.phone || "-"}","${supplier.address || "-"}","${supplier.notes || "-"}","${new Date(supplier.createdAt).toLocaleDateString("id-ID")}"\n`;
          });
        }

        setExportProgress(100);

        // Download CSV
        const blob = new Blob([csvContent], {
          type: "text/csv;charset=utf-8;",
        });
        const link = document.createElement("a");
        const url = URL.createObjectURL(blob);
        link.setAttribute("href", url);
        link.setAttribute(
          "download",
          `laporan-${exportConfig.reportType}-${new Date().toISOString().split("T")[0]}.csv`
        );
        link.style.visibility = "hidden";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }

      toast.success(
        `Laporan ${exportConfig.reportType} berhasil diekspor dengan format profesional!`
      );
      setShowExportDialog(false);
    } catch (error) {
      console.error("Export error:", error);
      toast.error("Gagal mengekspor laporan");
    } finally {
      setIsExporting(false);
      setExportProgress(0);
    }
  };

  const getDateRangeForReportType = () => {
    // Return custom date range based on user selection
    return "custom";
  };

  const getCustomDateRange = () => {
    switch (exportConfig.reportType) {
      case "harian":
        if (exportConfig.selectedDate) {
          const startDate = new Date(exportConfig.selectedDate);
          startDate.setHours(0, 0, 0, 0);
          const endDate = new Date(exportConfig.selectedDate);
          endDate.setHours(23, 59, 59, 999);
          return { startDate, endDate };
        }
        break;
      case "bulanan":
        if (
          exportConfig.selectedMonth !== undefined &&
          exportConfig.selectedYear
        ) {
          const startDate = new Date(
            exportConfig.selectedYear,
            exportConfig.selectedMonth,
            1
          );
          const endDate = new Date(
            exportConfig.selectedYear,
            exportConfig.selectedMonth + 1,
            0,
            23,
            59,
            59,
            999
          );
          return { startDate, endDate };
        }
        break;
      case "tahunan":
        if (exportConfig.selectedYear) {
          const startDate = new Date(exportConfig.selectedYear, 0, 1);
          const endDate = new Date(
            exportConfig.selectedYear,
            11,
            31,
            23,
            59,
            59,
            999
          );
          return { startDate, endDate };
        }
        break;
    }

    // Fallback to current period
    const now = new Date();
    return {
      startDate: new Date(now.getFullYear(), now.getMonth(), 1),
      endDate: new Date(
        now.getFullYear(),
        now.getMonth() + 1,
        0,
        23,
        59,
        59,
        999
      ),
    };
  };

  const getReportPeriodText = () => {
    switch (exportConfig.reportType) {
      case "harian":
        if (exportConfig.selectedDate) {
          return `Harian - ${exportConfig.selectedDate.toLocaleDateString("id-ID")}`;
        }
        return `Harian - ${new Date().toLocaleDateString("id-ID")}`;
      case "bulanan":
        if (
          exportConfig.selectedMonth !== undefined &&
          exportConfig.selectedYear
        ) {
          const monthNames = [
            "Januari",
            "Februari",
            "Maret",
            "April",
            "Mei",
            "Juni",
            "Juli",
            "Agustus",
            "September",
            "Oktober",
            "November",
            "Desember",
          ];
          return `Bulanan - ${monthNames[exportConfig.selectedMonth]} ${exportConfig.selectedYear}`;
        }
        return `Bulanan - ${new Date().toLocaleDateString("id-ID", { month: "long", year: "numeric" })}`;
      case "tahunan":
        if (exportConfig.selectedYear) {
          return `Tahunan - ${exportConfig.selectedYear}`;
        }
        return `Tahunan - ${new Date().getFullYear()}`;
      default:
        return "Custom";
    }
  };

  // Download template function
  const downloadTemplate = () => {
    try {
      const workbook = createProductImportTemplate();
      const fileName = `template-import-produk-${new Date().toISOString().split("T")[0]}.xlsx`;
      XLSX.writeFile(workbook, fileName);
      toast.success("Template berhasil diunduh!");
    } catch (error) {
      console.error("Template download error:", error);
      toast.error("Gagal mengunduh template");
    }
  };

  const handleImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      toast.error("Ukuran file maksimal 10MB");
      return;
    }

    // Validate file type
    if (!file.name.match(/\.(xlsx|xls)$/i)) {
      toast.error("Format file harus Excel (.xlsx atau .xls)");
      return;
    }

    setIsImporting(true);
    setImportProgress(0);
    setImportSummary(null);

    try {
      setImportProgress(20);

      const arrayBuffer = await file.arrayBuffer();
      setImportProgress(40);

      const result = await importProducts(arrayBuffer);
      setImportProgress(80);

      if (result.success) {
        setImportProgress(100);
        setImportSummary(result.summary);
        toast.success(
          result.success + " Lihat notifikasi untuk detail lengkap."
        );
      } else if (result.error) {
        setImportSummary(result.summary);
        toast.error(result.error);
      }
    } catch (error) {
      console.error("Import error:", error);
      toast.error("Gagal mengimpor file");
    } finally {
      setIsImporting(false);
      setImportProgress(0);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  const printReport = () => {
    window.print();
  };

  const shareReport = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: "Laporan Keuangan",
          text: "Laporan keuangan dari aplikasi kasir online",
          url: window.location.href,
        });
      } catch (error) {
        console.error("Error sharing:", error);
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
      toast.success("Link laporan disalin ke clipboard!");
    }
  };

  return (
    <div className="flex items-center gap-2">
      {/* Advanced Export Dialog */}
      <Dialog open={showExportDialog} onOpenChange={setShowExportDialog}>
        <DialogTrigger asChild>
          <Button
            variant="outline"
            className="flex items-center gap-2 cursor-pointer"
          >
            <Download className="h-4 w-4" />
            <span className="hidden sm:inline">Export Laporan</span>
          </Button>
        </DialogTrigger>
        <DialogContent className="max-w-3xl max-h-[80vh] flex flex-col">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Konfigurasi Export Laporan
            </DialogTitle>
            <DialogDescription>
              Pilih jenis laporan, periode, dan format yang ingin diekspor
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-y-auto space-y-4 p-2">
            {/* Report Type Selection */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Jenis Laporan</Label>
              <div className="grid grid-cols-3 gap-2">
                {[
                  { value: "harian", label: "Harian", icon: Calendar },
                  { value: "bulanan", label: "Bulanan", icon: Calendar },
                  { value: "tahunan", label: "Tahunan", icon: Calendar },
                ].map((type) => (
                  <Card
                    key={type.value}
                    className={`cursor-pointer transition-all hover:shadow-sm ${
                      exportConfig.reportType === type.value
                        ? "ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-950"
                        : "hover:bg-slate-50 dark:hover:bg-slate-800"
                    }`}
                    onClick={() =>
                      setExportConfig((prev) => ({
                        ...prev,
                        reportType: type.value as any,
                      }))
                    }
                  >
                    <CardContent className="p-3 text-center">
                      <type.icon className="h-5 w-5 mx-auto mb-1 text-blue-500" />
                      <p className="text-xs font-medium">{type.label}</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>

            <Separator />

            {/* Date/Period Selection */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Pilih Periode</Label>

              {exportConfig.reportType === "harian" && (
                <div>
                  <DatePicker
                    date={exportConfig.selectedDate}
                    setDate={(date) => {
                      setExportConfig((prev) => ({
                        ...prev,
                        selectedDate: date || new Date(),
                      }));
                    }}
                    placeholder="Pilih tanggal"
                    className="w-full h-9"
                  />
                </div>
              )}

              {exportConfig.reportType === "bulanan" && (
                <div className="grid grid-cols-2 gap-2">
                  <Select
                    value={exportConfig.selectedMonth?.toString() || ""}
                    onValueChange={(value) => {
                      setExportConfig((prev) => ({
                        ...prev,
                        selectedMonth: parseInt(value),
                      }));
                    }}
                  >
                    <SelectTrigger className="h-9">
                      <SelectValue placeholder="Bulan" />
                    </SelectTrigger>
                    <SelectContent>
                      {[
                        "Jan",
                        "Feb",
                        "Mar",
                        "Apr",
                        "Mei",
                        "Jun",
                        "Jul",
                        "Agu",
                        "Sep",
                        "Okt",
                        "Nov",
                        "Des",
                      ].map((month, index) => (
                        <SelectItem key={index} value={index.toString()}>
                          {month}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Input
                    type="number"
                    min="2020"
                    max="2030"
                    placeholder="Tahun"
                    value={exportConfig.selectedYear || ""}
                    onChange={(e) => {
                      setExportConfig((prev) => ({
                        ...prev,
                        selectedYear:
                          parseInt(e.target.value) || new Date().getFullYear(),
                      }));
                    }}
                    className="h-9"
                  />
                </div>
              )}

              {exportConfig.reportType === "tahunan" && (
                <div>
                  <Input
                    type="number"
                    min="2020"
                    max="2030"
                    placeholder="Tahun"
                    value={exportConfig.selectedYear || ""}
                    onChange={(e) => {
                      setExportConfig((prev) => ({
                        ...prev,
                        selectedYear:
                          parseInt(e.target.value) || new Date().getFullYear(),
                      }));
                    }}
                    className="w-full h-9"
                  />
                </div>
              )}
            </div>

            <Separator />

            {/* Sections Selection */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Bagian Laporan</Label>
              <div className="grid grid-cols-2 gap-2">
                {[
                  { key: "penjualan", label: "Data Penjualan" },
                  { key: "pembelian", label: "Data Pembelian" },
                  { key: "produk", label: "Data Produk" },
                  { key: "pelanggan", label: "Data Pelanggan" },
                  { key: "supplier", label: "Data Supplier" },
                ].map((section) => (
                  <div
                    key={section.key}
                    className="flex items-center space-x-2 p-2 border rounded"
                  >
                    <Checkbox
                      id={section.key}
                      checked={
                        exportConfig.sections[
                          section.key as keyof typeof exportConfig.sections
                        ]
                      }
                      onCheckedChange={(checked) =>
                        setExportConfig((prev) => ({
                          ...prev,
                          sections: {
                            ...prev.sections,
                            [section.key]: checked,
                          },
                        }))
                      }
                    />
                    <Label
                      htmlFor={section.key}
                      className="text-sm cursor-pointer flex-1"
                    >
                      {section.label}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            <Separator />

            {/* Format and Options */}
            <div className="space-y-3">
              <div className="space-y-2">
                <Label className="text-sm font-medium">Format Export</Label>
                <Select
                  value={exportConfig.format}
                  onValueChange={(value) =>
                    setExportConfig((prev) => ({
                      ...prev,
                      format: value as any,
                    }))
                  }
                >
                  <SelectTrigger className="h-9">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="excel">
                      <div className="flex items-center gap-2">
                        <FileSpreadsheet className="h-4 w-4" />
                        Excel (.xlsx)
                      </div>
                    </SelectItem>
                    <SelectItem value="csv">
                      <div className="flex items-center gap-2">
                        <FileText className="h-4 w-4" />
                        CSV (.csv)
                      </div>
                    </SelectItem>
                    <SelectItem value="pdf" disabled>
                      <div className="flex items-center gap-2">
                        <FileImage className="h-4 w-4" />
                        PDF (Segera Hadir)
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium">Opsi Tambahan</Label>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="includeSummary"
                      checked={exportConfig.includeSummary}
                      onCheckedChange={(checked) =>
                        setExportConfig((prev) => ({
                          ...prev,
                          includeSummary: checked as boolean,
                        }))
                      }
                    />
                    <Label
                      htmlFor="includeSummary"
                      className="text-sm cursor-pointer"
                    >
                      Sertakan ringkasan
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="includeCharts"
                      checked={exportConfig.includeCharts}
                      onCheckedChange={(checked) =>
                        setExportConfig((prev) => ({
                          ...prev,
                          includeCharts: checked as boolean,
                        }))
                      }
                      disabled
                    />
                    <Label
                      htmlFor="includeCharts"
                      className="text-sm cursor-pointer text-slate-500"
                    >
                      Sertakan grafik (Segera Hadir)
                    </Label>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons - Fixed at bottom */}
          <div className="flex-shrink-0 flex justify-between pt-4 border-t bg-white dark:bg-gray-900">
            <Button
              variant="outline"
              onClick={() => setShowExportDialog(false)}
            >
              Batal
            </Button>
            <Button
              onClick={generateAdvancedExport}
              disabled={!Object.values(exportConfig.sections).some(Boolean)}
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              Export Laporan
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Enhanced Import Dialog */}
      <Dialog open={showImportDialog} onOpenChange={setShowImportDialog}>
        <DialogTrigger asChild>
          <Button
            variant="outline"
            className="flex items-center gap-2 cursor-pointer"
          >
            <Upload className="h-4 w-4" />
            <span className="hidden sm:inline">Import Produk</span>
          </Button>
        </DialogTrigger>
        <DialogContent className="max-w-2xl max-h-[80vh] flex flex-col">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle className="flex items-center gap-2">
              <Upload className="h-5 w-5" />
              Import Data Produk
            </DialogTitle>
            <DialogDescription>
              Upload file Excel untuk mengimpor data produk ke sistem
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-y-auto space-y-4 p-2">
            {/* Instructions */}
            <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-lg">
              <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
                Petunjuk Import:
              </h4>
              <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                <li>
                  • <strong>Kolom Wajib:</strong> Nama Produk, Harga Jual
                </li>
                <li>
                  • <strong>Kolom Opsional:</strong> SKU, Barcode, Kategori,
                  Satuan, dll
                </li>
                <li>
                  • <strong>Format:</strong> File Excel (.xlsx atau .xls)
                </li>
                <li>
                  • <strong>Maksimal:</strong> 1000 produk per import, file 10MB
                </li>
                <li>
                  • <strong>Stok awal:</strong> Akan diset ke 0 (kelola via
                  pembelian)
                </li>
              </ul>
            </div>

            {/* Download Template Button */}
            <div className="flex justify-center">
              <Button
                onClick={downloadTemplate}
                variant="outline"
                className="flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                Download Template Excel
              </Button>
            </div>

            <Separator />

            {/* File Upload Area */}
            <div className="border-2 border-dashed border-slate-300 rounded-lg p-6 text-center">
              <Upload className="h-12 w-12 mx-auto text-slate-400 mb-4" />
              <p className="text-sm text-slate-600 mb-2">
                Klik untuk memilih file atau drag & drop
              </p>
              <p className="text-xs text-slate-500">
                Format: .xlsx, .xls (Maksimal 10MB)
              </p>
              <input
                ref={fileInputRef}
                type="file"
                accept=".xlsx,.xls"
                onChange={handleImport}
                className="hidden"
              />
              <Button
                variant="outline"
                onClick={() => fileInputRef.current?.click()}
                className="mt-4"
                disabled={isImporting}
              >
                {isImporting ? "Memproses..." : "Pilih File Excel"}
              </Button>
            </div>

            {/* Import Progress */}
            {isImporting && (
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Mengimpor data produk...</span>
                  <span>{importProgress}%</span>
                </div>
                <Progress value={importProgress} />
              </div>
            )}

            {/* Import Summary */}
            {importSummary && (
              <div className="bg-slate-50 dark:bg-slate-800 p-4 rounded-lg">
                <h4 className="font-medium mb-2">Hasil Import:</h4>
                <div className="text-sm space-y-1">
                  <p>
                    ✅ Produk berhasil dibuat: {importSummary.productsCreated}
                  </p>
                  <p>
                    📁 Kategori baru dibuat: {importSummary.categoriesCreated}
                  </p>
                  <p>📏 Satuan baru dibuat: {importSummary.unitsCreated}</p>
                  <p>🎨 Varian dibuat: {importSummary.variantsCreated}</p>

                  {importSummary.errors && importSummary.errors.length > 0 && (
                    <div className="mt-3">
                      <p className="text-red-600 font-medium">❌ Error:</p>
                      <div className="max-h-32 overflow-y-auto">
                        {importSummary.errors
                          .slice(0, 10)
                          .map((error: string, index: number) => (
                            <p key={index} className="text-xs text-red-600">
                              • {error}
                            </p>
                          ))}
                        {importSummary.errors.length > 10 && (
                          <p className="text-xs text-red-600">
                            ... dan {importSummary.errors.length - 10} error
                            lainnya
                          </p>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Future Import Types (Placeholder) */}
            <div className="bg-yellow-50 dark:bg-yellow-950 p-4 rounded-lg">
              <h4 className="font-medium text-yellow-900 dark:text-yellow-100 mb-2">
                🚧 Segera Hadir:
              </h4>
              <p className="text-sm text-yellow-800 dark:text-yellow-200">
                Import untuk Penjualan dan Pembelian akan tersedia dalam update
                mendatang.
              </p>
            </div>

            {/* Close Button */}
            {importSummary && (
              <div className="flex justify-end pt-4">
                <Button
                  onClick={() => {
                    setShowImportDialog(false);
                    setImportSummary(null);
                    if (fileInputRef.current) {
                      fileInputRef.current.value = "";
                    }
                  }}
                  className="flex items-center gap-2"
                >
                  Tutup
                </Button>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Export Progress Dialog */}
      {isExporting && (
        <Dialog open={isExporting}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Download className="h-5 w-5" />
                Mengekspor Laporan
              </DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div className="flex items-center justify-between text-sm">
                <span>Memproses data...</span>
                <span>{exportProgress}%</span>
              </div>
              <Progress value={exportProgress} />
              <p className="text-sm text-slate-600">
                Mohon tunggu, sedang memproses dan mengunduh file laporan.
              </p>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};
