"use client";

import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON><PERSON>3,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  TrendingUp,
  Calendar,
  Filter,
  Download,
  Maximize2,
} from "lucide-react";
import {
  Bar<PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  Legend,
  <PERSON><PERSON>hart as RechartsLine<PERSON>hart,
  Line,
  AreaChart,
  Area,
  Composed<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>atter<PERSON>hart,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
} from "recharts";
import {
  getSalesReportData,
  getPurchaseReportData,
  getProductReportData,
} from "@/actions/reports/reports";

interface FilterState {
  dateRange: string;
  startDate?: Date;
  endDate?: Date;
  category?: string;
  supplier?: string;
  customer?: string;
  status?: string;
}

interface DataVisualizationProps {
  filters: FilterState;
}

const formatCurrency = (value: number) => {
  return new Intl.NumberFormat("id-ID", {
    style: "currency",
    currency: "IDR",
    minimumFractionDigits: 0,
  }).format(value);
};

const CHART_COLORS = [
  "#3B82F6",
  "#10B981",
  "#F59E0B",
  "#EF4444",
  "#8B5CF6",
  "#EC4899",
  "#06B6D4",
  "#84CC16",
];

export const DataVisualization: React.FC<DataVisualizationProps> = ({
  filters,
}) => {
  const [loading, setLoading] = useState(true);
  const [salesData, setSalesData] = useState<any[]>([]);
  const [purchaseData, setPurchaseData] = useState<any[]>([]);
  const [productData, setProductData] = useState<any[]>([]);
  const [chartData, setChartData] = useState<any>({});

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const [salesResult, purchaseResult, productResult] = await Promise.all([
          getSalesReportData(filters.dateRange),
          getPurchaseReportData(filters.dateRange),
          getProductReportData(filters.dateRange),
        ]);

        if (salesResult.success && salesResult.data) {
          setSalesData(salesResult.data);
        }

        if (purchaseResult.success && purchaseResult.data) {
          setPurchaseData(purchaseResult.data);
        }

        if (productResult.success && productResult.data) {
          setProductData(productResult.data);
        }

        // Prepare various chart data
        prepareChartData(
          salesResult.data || [],
          purchaseResult.data || [],
          productResult.data || []
        );
      } catch (error) {
        console.error("Error fetching visualization data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [filters]);

  const prepareChartData = (
    sales: any[],
    purchases: any[],
    products: any[]
  ) => {
    console.log("Preparing chart data with:", {
      salesCount: sales.length,
      purchasesCount: purchases.length,
      productsCount: products.length,
      sampleProduct: products[0],
    });
    // Monthly comparison data - using real data
    const monthlyData = [];
    for (let i = 11; i >= 0; i--) {
      const month = new Date();
      month.setMonth(month.getMonth() - i);
      const monthName = month.toLocaleDateString("id-ID", { month: "short" });
      const monthStart = new Date(month.getFullYear(), month.getMonth(), 1);
      const monthEnd = new Date(month.getFullYear(), month.getMonth() + 1, 0);

      // Calculate real sales for this month
      const monthlySales = sales
        .filter((sale) => {
          const saleDate = new Date(sale.date);
          return saleDate >= monthStart && saleDate <= monthEnd;
        })
        .reduce((sum, sale) => sum + sale.total, 0);

      // Calculate real purchases for this month
      const monthlyPurchases = purchases
        .filter((purchase) => {
          const purchaseDate = new Date(purchase.date);
          return purchaseDate >= monthStart && purchaseDate <= monthEnd;
        })
        .reduce((sum, purchase) => sum + purchase.total, 0);

      // Calculate profit (sales - purchases)
      const monthlyProfit = monthlySales - monthlyPurchases;

      monthlyData.push({
        month: monthName,
        sales: monthlySales,
        purchases: monthlyPurchases,
        profit: monthlyProfit,
      });
    }

    // Category distribution
    const categoryData = products.reduce((acc: any, product: any) => {
      const category = product.category?.name || "Lainnya";
      if (!acc[category]) {
        acc[category] = { name: category, value: 0, count: 0 };
      }
      acc[category].value += product.revenue || 0;
      acc[category].count += 1;
      return acc;
    }, {});

    const categoryPieData = Object.values(categoryData).map(
      (item: any, index) => ({
        ...item,
        color: CHART_COLORS[index % CHART_COLORS.length],
      })
    );

    console.log("Category distribution data:", {
      categoryData,
      categoryPieData,
    });

    // Top products
    const topProducts = products
      .sort((a, b) => (b.revenue || 0) - (a.revenue || 0))
      .slice(0, 10)
      .map((product) => ({
        name:
          product.name.length > 15
            ? product.name.substring(0, 15) + "..."
            : product.name,
        revenue: product.revenue || 0,
        sold: product.sold || 0,
        stock: product.stock || 0,
      }));

    // Daily sales trend (last 30 days) - using real data
    const dailyTrend = [];
    for (let i = 29; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const dateString = date.toISOString().split("T")[0]; // YYYY-MM-DD format

      // Calculate real sales for this day
      const dailySales = sales
        .filter((sale) => {
          const saleDate = new Date(sale.date).toISOString().split("T")[0];
          return saleDate === dateString;
        })
        .reduce((sum, sale) => sum + sale.total, 0);

      // Count real transactions for this day
      const dailyTransactions = sales.filter((sale) => {
        const saleDate = new Date(sale.date).toISOString().split("T")[0];
        return saleDate === dateString;
      }).length;

      dailyTrend.push({
        date: date.toLocaleDateString("id-ID", {
          day: "2-digit",
          month: "2-digit",
        }),
        sales: dailySales,
        transactions: dailyTransactions,
      });
    }

    // Performance radar data - using real metrics
    const totalSales = sales.reduce((sum, sale) => sum + sale.total, 0);
    const totalPurchases = purchases.reduce(
      (sum, purchase) => sum + purchase.total,
      0
    );
    const totalProfit = totalSales - totalPurchases;
    const totalProducts = products.length;
    const activeProducts = products.filter((p) => (p.stock || 0) > 0).length;
    const profitMargin = totalSales > 0 ? (totalProfit / totalSales) * 100 : 0;

    // Calculate performance scores (0-100 scale)
    const maxSales = 50000000; // Adjust based on business scale
    const maxProducts = 1000; // Adjust based on business scale

    const performanceData = [
      {
        subject: "Penjualan",
        A: Math.min((totalSales / maxSales) * 100, 100),
        fullMark: 100,
      },
      {
        subject: "Keuntungan",
        A: Math.max(0, Math.min(profitMargin, 100)),
        fullMark: 100,
      },
      {
        subject: "Transaksi",
        A: Math.min((sales.length / 100) * 100, 100),
        fullMark: 100,
      },
      {
        subject: "Produk",
        A: Math.min((totalProducts / maxProducts) * 100, 100),
        fullMark: 100,
      },
      {
        subject: "Efisiensi",
        A: totalProducts > 0 ? (activeProducts / totalProducts) * 100 : 0,
        fullMark: 100,
      },
      {
        subject: "Pertumbuhan",
        A:
          monthlyData.length > 1
            ? Math.max(
                0,
                Math.min(
                  ((monthlyData[monthlyData.length - 1].sales -
                    monthlyData[monthlyData.length - 2].sales) /
                    Math.max(monthlyData[monthlyData.length - 2].sales, 1)) *
                    100 +
                    50,
                  100
                )
              )
            : 50,
        fullMark: 100,
      },
    ];

    // Scatter plot data (Sales vs Profit)
    const scatterData = products.slice(0, 20).map((product) => ({
      x: product.revenue || 0,
      y: product.profit || 0,
      name: product.name,
    }));

    setChartData({
      monthly: monthlyData,
      categoryPie: categoryPieData,
      topProducts,
      dailyTrend,
      performance: performanceData,
      scatter: scatterData,
    });
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-6 bg-slate-200 rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="h-64 bg-slate-200 rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Chart Type Tabs */}
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="trends" className="flex items-center gap-2">
            <LineChart className="h-4 w-4" />
            Tren
          </TabsTrigger>
          <TabsTrigger value="distribution" className="flex items-center gap-2">
            <PieChart className="h-4 w-4" />
            Distribusi
          </TabsTrigger>
          <TabsTrigger value="performance" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Performa
          </TabsTrigger>
        </TabsList>

        {/* Overview Charts */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
            >
              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <CardTitle>Perbandingan Bulanan</CardTitle>
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4" />
                  </Button>
                </CardHeader>
                <CardContent>
                  <div className="h-[350px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <ComposedChart data={chartData.monthly}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="month" />
                        <YAxis
                          tickFormatter={(value) => formatCurrency(value)}
                        />
                        <Tooltip
                          formatter={(value) => formatCurrency(value as number)}
                        />
                        <Legend />
                        <Bar dataKey="sales" fill="#10B981" name="Penjualan" />
                        <Bar
                          dataKey="purchases"
                          fill="#EF4444"
                          name="Pembelian"
                        />
                        <Line
                          type="monotone"
                          dataKey="profit"
                          stroke="#3B82F6"
                          strokeWidth={3}
                          name="Keuntungan"
                        />
                      </ComposedChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle>Produk Terlaris</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-[350px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart
                        data={chartData.topProducts}
                        layout="horizontal"
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis
                          type="number"
                          tickFormatter={(value) => formatCurrency(value)}
                        />
                        <YAxis dataKey="name" type="category" width={100} />
                        <Tooltip
                          formatter={(value) => formatCurrency(value as number)}
                        />
                        <Bar dataKey="revenue" fill="#8B5CF6" />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </TabsContent>

        {/* Trends Charts */}
        <TabsContent value="trends" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle>Tren Penjualan Harian</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-[350px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <AreaChart data={chartData.dailyTrend}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="date" />
                        <YAxis
                          tickFormatter={(value) => formatCurrency(value)}
                        />
                        <Tooltip
                          formatter={(value) => formatCurrency(value as number)}
                        />
                        <Area
                          type="monotone"
                          dataKey="sales"
                          stroke="#10B981"
                          fill="#10B981"
                          fillOpacity={0.6}
                          name="Penjualan"
                        />
                      </AreaChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle>Jumlah Transaksi</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-[350px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <RechartsLineChart data={chartData.dailyTrend}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="date" />
                        <YAxis />
                        <Tooltip />
                        <Line
                          type="monotone"
                          dataKey="transactions"
                          stroke="#3B82F6"
                          strokeWidth={3}
                          name="Transaksi"
                        />
                      </RechartsLineChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </TabsContent>

        {/* Distribution Charts */}
        <TabsContent value="distribution" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle>Distribusi Kategori Produk</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-[350px]">
                    {chartData.categoryPie &&
                    chartData.categoryPie.length > 0 ? (
                      <ResponsiveContainer width="100%" height="100%">
                        <RechartsPieChart>
                          <Pie
                            data={chartData.categoryPie}
                            cx="50%"
                            cy="50%"
                            outerRadius={100}
                            dataKey="value"
                            label={({ name, percent }) =>
                              `${name} ${(percent * 100).toFixed(0)}%`
                            }
                          >
                            {chartData.categoryPie?.map(
                              (entry: any, index: number) => (
                                <Cell
                                  key={`cell-${index}`}
                                  fill={entry.color}
                                />
                              )
                            )}
                          </Pie>
                          <Tooltip
                            formatter={(value) =>
                              formatCurrency(value as number)
                            }
                          />
                          <Legend />
                        </RechartsPieChart>
                      </ResponsiveContainer>
                    ) : (
                      <div className="flex items-center justify-center h-full text-gray-500 dark:text-gray-400">
                        <div className="text-center">
                          <PieChart className="h-12 w-12 mx-auto mb-4 opacity-50" />
                          <p>Tidak ada data distribusi kategori</p>
                          <p className="text-sm mt-1">
                            Data akan muncul setelah ada transaksi penjualan
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle>Jumlah Produk per Kategori</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-[350px]">
                    {chartData.categoryPie &&
                    chartData.categoryPie.length > 0 ? (
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart data={chartData.categoryPie}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="name" />
                          <YAxis />
                          <Tooltip />
                          <Bar
                            dataKey="count"
                            fill="#F59E0B"
                            name="Jumlah Produk"
                          />
                        </BarChart>
                      </ResponsiveContainer>
                    ) : (
                      <div className="flex items-center justify-center h-full text-gray-500 dark:text-gray-400">
                        <div className="text-center">
                          <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                          <p>Tidak ada data jumlah produk per kategori</p>
                          <p className="text-sm mt-1">
                            Data akan muncul setelah produk ditambahkan
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </TabsContent>

        {/* Performance Charts */}
        <TabsContent value="performance" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle>Radar Performa Bisnis</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-[350px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <RadarChart data={chartData.performance}>
                        <PolarGrid />
                        <PolarAngleAxis dataKey="subject" />
                        <PolarRadiusAxis angle={90} domain={[0, 100]} />
                        <Radar
                          name="Performa"
                          dataKey="A"
                          stroke="#3B82F6"
                          fill="#3B82F6"
                          fillOpacity={0.3}
                        />
                      </RadarChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle>Analisis Penjualan vs Keuntungan</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-[350px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <ScatterChart data={chartData.scatter}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis
                          dataKey="x"
                          name="Penjualan"
                          tickFormatter={(value) => formatCurrency(value)}
                        />
                        <YAxis
                          dataKey="y"
                          name="Keuntungan"
                          tickFormatter={(value) => formatCurrency(value)}
                        />
                        <Tooltip
                          formatter={(value, name) => [
                            formatCurrency(value as number),
                            name === "x" ? "Penjualan" : "Keuntungan",
                          ]}
                        />
                        <Scatter dataKey="y" fill="#8B5CF6" />
                      </ScatterChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};
