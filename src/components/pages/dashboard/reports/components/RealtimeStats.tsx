"use client";

import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Activity,
  Clock,
  Users,
  ShoppingCart,
  TrendingUp,
  TrendingDown,
  Zap,
  Eye,
  RefreshCw,
  Wifi,
  WifiOff,
} from "lucide-react";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  AreaChart,
  Area,
} from "recharts";
import {
  getSalesReportData,
  getPurchaseReportData,
  getProductReportData,
  getServicesReportData,
} from "@/actions/reports/reports";

interface FilterState {
  dateRange: string;
  startDate?: Date;
  endDate?: Date;
  category?: string;
  supplier?: string;
  customer?: string;
  status?: string;
}

interface RealtimeStatsProps {
  filters: FilterState;
  refreshInterval: number | null;
}

interface RealtimeMetric {
  id: string;
  title: string;
  value: string;
  change: number;
  icon: React.ReactNode;
  color: string;
  status: "up" | "down" | "stable";
}

interface ActivityLog {
  id: string;
  type: "sale" | "purchase" | "user" | "system";
  message: string;
  timestamp: Date;
  amount?: number;
}

const formatCurrency = (value: number) => {
  return new Intl.NumberFormat("id-ID", {
    style: "currency",
    currency: "IDR",
    minimumFractionDigits: 0,
  }).format(value);
};

export const RealtimeStats: React.FC<RealtimeStatsProps> = ({
  filters,
  refreshInterval,
}) => {
  const [isConnected, setIsConnected] = useState(true);
  const [lastUpdate, setLastUpdate] = useState(new Date());
  const [metrics, setMetrics] = useState<RealtimeMetric[]>([]);
  const [activityLogs, setActivityLogs] = useState<ActivityLog[]>([]);
  const [realtimeData, setRealtimeData] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [salesData, setSalesData] = useState<any[]>([]);
  const [purchaseData, setPurchaseData] = useState<any[]>([]);
  const [productData, setProductData] = useState<any[]>([]);
  const [servicesData, setServicesData] = useState<any[]>([]);

  // Fetch real data from database
  const fetchRealData = async () => {
    try {
      console.log("Fetching real-time data from database...");
      const [salesResult, purchaseResult, productResult, servicesResult] =
        await Promise.all([
          getSalesReportData("today"),
          getPurchaseReportData("today"),
          getProductReportData("today"),
          getServicesReportData("today"),
        ]);

      if (salesResult.success && salesResult.data) {
        setSalesData(salesResult.data);
        console.log("Sales data fetched:", salesResult.data.length, "records");
      }

      if (purchaseResult.success && purchaseResult.data) {
        setPurchaseData(purchaseResult.data);
        console.log(
          "Purchase data fetched:",
          purchaseResult.data.length,
          "records"
        );
      }

      if (productResult.success && productResult.data) {
        setProductData(productResult.data);
        console.log(
          "Product data fetched:",
          productResult.data.length,
          "records"
        );
      }

      if (servicesResult.success && servicesResult.data) {
        setServicesData(servicesResult.data);
        console.log(
          "Services data fetched:",
          servicesResult.data.length,
          "records"
        );
      }
    } catch (error) {
      console.error("Error fetching real-time data:", error);
    }
  };

  // Generate real-time data updates using actual database data
  useEffect(() => {
    const generateRealtimeMetrics = () => {
      const now = new Date();

      // Calculate real metrics from database data
      const todaySales = salesData.reduce((sum, sale) => sum + sale.total, 0);
      const todayTransactions = salesData.length;
      const todayServices = servicesData.length;
      const activeProducts = productData.filter(
        (p) => (p.stock || 0) > 0
      ).length;

      // Calculate hourly transaction rate
      const currentHour = now.getHours();
      const transactionsThisHour = salesData.filter((sale) => {
        const saleDate = new Date(sale.date);
        return saleDate.getHours() === currentHour;
      }).length;

      // Calculate conversion rate (services completed vs received)
      const completedServices = servicesData.filter(
        (s) => s.status === "SELESAI_SUDAH_DIAMBIL"
      ).length;
      const conversionRate =
        servicesData.length > 0
          ? (completedServices / servicesData.length) * 100
          : 0;

      const baseMetrics: RealtimeMetric[] = [
        {
          id: "active-products",
          title: "Produk Aktif",
          value: activeProducts.toString(),
          change: Math.random() * 10 - 5, // Could be calculated from historical data
          icon: <Users className="h-5 w-5 text-white" />,
          color: "bg-blue-500",
          status: activeProducts > productData.length * 0.8 ? "up" : "down",
        },
        {
          id: "sales-today",
          title: "Penjualan Hari Ini",
          value: formatCurrency(todaySales),
          change: Math.random() * 30 - 15, // Could be calculated from yesterday's data
          icon: <ShoppingCart className="h-5 w-5 text-white" />,
          color: "bg-green-500",
          status: todaySales > 0 ? "up" : "stable",
        },
        {
          id: "transactions",
          title: "Transaksi Hari Ini",
          value: todayTransactions.toString(),
          change: Math.random() * 25 - 12.5, // Could be calculated from historical data
          icon: <Activity className="h-5 w-5 text-white" />,
          color: "bg-purple-500",
          status: todayTransactions > 0 ? "up" : "stable",
        },
        {
          id: "conversion",
          title: "Tingkat Penyelesaian Servis",
          value: `${conversionRate.toFixed(1)}%`,
          change: Math.random() * 15 - 7.5, // Could be calculated from historical data
          icon: <TrendingUp className="h-5 w-5 text-white" />,
          color: "bg-amber-500",
          status: conversionRate > 50 ? "up" : "down",
        },
        {
          id: "services-today",
          title: "Servis Hari Ini",
          value: todayServices.toString(),
          change: Math.random() * 20 - 10, // Could be calculated from historical data
          icon: <Eye className="h-5 w-5 text-white" />,
          color: "bg-indigo-500",
          status: todayServices > 0 ? "up" : "stable",
        },
      ];

      setMetrics(baseMetrics);
      setLastUpdate(now);
    };

    const generateActivityLogs = () => {
      const activities: ActivityLog[] = [];

      // Add real sales activities
      salesData.slice(0, 5).forEach((sale, index) => {
        activities.push({
          id: `sale-${sale.id}`,
          type: "sale",
          message: `Penjualan ${sale.transactionNumber || sale.id}`,
          timestamp: new Date(sale.date),
          amount: sale.total,
        });
      });

      // Add real purchase activities
      purchaseData.slice(0, 3).forEach((purchase, index) => {
        activities.push({
          id: `purchase-${purchase.id}`,
          type: "purchase",
          message: `Pembelian ${purchase.transactionNumber || purchase.id}`,
          timestamp: new Date(purchase.date),
          amount: purchase.total,
        });
      });

      // Add real service activities
      servicesData.slice(0, 2).forEach((service, index) => {
        activities.push({
          id: `service-${service.id}`,
          type: "user",
          message: `Servis ${service.serviceNumber} - ${service.deviceType}`,
          timestamp: new Date(service.receivedDate),
          amount: service.finalCost || service.estimatedCost,
        });
      });

      // Sort by timestamp (most recent first) and limit to 10
      const sortedActivities = activities
        .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
        .slice(0, 10);

      setActivityLogs(sortedActivities);
    };

    const generateRealtimeChartData = () => {
      const data = [];
      const now = new Date();

      for (let i = 29; i >= 0; i--) {
        const time = new Date(now.getTime() - i * 60000); // Last 30 minutes
        const timeString = time.toISOString();

        // Calculate real sales for this time period
        const periodSales = salesData
          .filter((sale) => {
            const saleTime = new Date(sale.date);
            return Math.abs(saleTime.getTime() - time.getTime()) < 60000; // Within 1 minute
          })
          .reduce((sum, sale) => sum + sale.total, 0);

        // Count real transactions for this time period
        const periodTransactions = salesData.filter((sale) => {
          const saleTime = new Date(sale.date);
          return Math.abs(saleTime.getTime() - time.getTime()) < 60000; // Within 1 minute
        }).length;

        // Count active services for this time period
        const periodServices = servicesData.filter((service) => {
          const serviceTime = new Date(service.receivedDate);
          return Math.abs(serviceTime.getTime() - time.getTime()) < 60000; // Within 1 minute
        }).length;

        data.push({
          time: time.toLocaleTimeString("id-ID", {
            hour: "2-digit",
            minute: "2-digit",
          }),
          sales: periodSales,
          users: periodServices, // Using services as "users" metric
          transactions: periodTransactions,
        });
      }

      setRealtimeData(data);
    };

    // Initial load - fetch real data first
    const initializeData = async () => {
      await fetchRealData();
      generateRealtimeMetrics();
      generateActivityLogs();
      generateRealtimeChartData();
      setLoading(false);
    };

    initializeData();

    // Set up refresh interval
    let interval: NodeJS.Timeout;
    if (refreshInterval) {
      interval = setInterval(async () => {
        console.log("Refreshing real-time data...");
        await fetchRealData();
        generateRealtimeMetrics();
        generateActivityLogs();
        generateRealtimeChartData();

        // Simulate occasional connection issues (reduced frequency for real data)
        if (Math.random() < 0.02) {
          setIsConnected(false);
          setTimeout(() => setIsConnected(true), 1000);
        }
      }, refreshInterval * 1000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [refreshInterval, salesData, purchaseData, productData, servicesData]);

  const getActivityIcon = (type: ActivityLog["type"]) => {
    switch (type) {
      case "sale":
        return <ShoppingCart className="h-4 w-4 text-green-500" />;
      case "purchase":
        return <TrendingDown className="h-4 w-4 text-blue-500" />;
      case "user":
        return <Users className="h-4 w-4 text-purple-500" />;
      case "system":
        return <Zap className="h-4 w-4 text-amber-500" />;
      default:
        return <Activity className="h-4 w-4 text-slate-500" />;
    }
  };

  const getActivityColor = (type: ActivityLog["type"]) => {
    switch (type) {
      case "sale":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      case "purchase":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";
      case "user":
        return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200";
      case "system":
        return "bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200";
      default:
        return "bg-slate-100 text-slate-800 dark:bg-slate-900 dark:text-slate-200";
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-5 gap-6">
          {[...Array(5)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-slate-200 rounded w-3/4"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-slate-200 rounded w-1/2 mb-2"></div>
                <div className="h-4 bg-slate-200 rounded w-full"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Connection Status */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div
            className={`flex items-center gap-2 px-3 py-1 rounded-full text-sm ${
              isConnected
                ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
            }`}
          >
            {isConnected ? (
              <Wifi className="h-4 w-4" />
            ) : (
              <WifiOff className="h-4 w-4" />
            )}
            {isConnected ? "Terhubung" : "Terputus"}
          </div>
          <div className="flex items-center gap-2 text-sm text-slate-600 dark:text-slate-400">
            <Clock className="h-4 w-4" />
            Update terakhir: {lastUpdate.toLocaleTimeString("id-ID")}
          </div>
        </div>

        {refreshInterval && (
          <div className="flex items-center gap-2 text-sm text-slate-600 dark:text-slate-400">
            <RefreshCw className="h-4 w-4 animate-spin" />
            Refresh setiap {refreshInterval}s
          </div>
        )}
      </div>

      {/* Real-time Metrics */}
      <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-5 gap-6">
        {metrics.map((metric, index) => (
          <motion.div
            key={metric.id}
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card className="relative overflow-hidden gap-4 py-2">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 px-2 sm:px-4">
                <CardTitle className="text-sm font-medium text-slate-600 dark:text-slate-400">
                  {metric.title}
                </CardTitle>
                <div className={`p-2 rounded-lg shadow-md ${metric.color}`}>
                  {metric.icon}
                </div>
              </CardHeader>
              <CardContent className="px-2 sm:px-4">
                <div className="text-2xl font-bold text-slate-900 dark:text-white mb-1">
                  {metric.value}
                </div>
                <div className="flex items-center text-sm">
                  {metric.status === "up" ? (
                    <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                  ) : metric.status === "down" ? (
                    <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
                  ) : (
                    <Activity className="h-4 w-4 text-slate-500 mr-1" />
                  )}
                  <span
                    className={
                      metric.status === "up"
                        ? "text-green-600"
                        : metric.status === "down"
                          ? "text-red-600"
                          : "text-slate-600"
                    }
                  >
                    {Math.abs(metric.change).toFixed(1)}%
                  </span>
                  <span className="text-slate-500 ml-1">dari sebelumnya</span>
                </div>
              </CardContent>
              <div
                className={`absolute bottom-0 left-0 right-0 h-1 ${metric.color}`}
              />
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Real-time Charts and Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Real-time Chart */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Aktivitas Real-time (30 Menit Terakhir)
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={realtimeData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="time" />
                    <YAxis />
                    <Tooltip
                      formatter={(value, name) => [
                        name === "sales"
                          ? formatCurrency(value as number)
                          : value,
                        name === "sales"
                          ? "Penjualan"
                          : name === "users"
                            ? "Pengguna"
                            : "Transaksi",
                      ]}
                    />
                    <Area
                      type="monotone"
                      dataKey="sales"
                      stroke="#10B981"
                      fill="#10B981"
                      fillOpacity={0.3}
                    />
                    <Area
                      type="monotone"
                      dataKey="users"
                      stroke="#3B82F6"
                      fill="#3B82F6"
                      fillOpacity={0.3}
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Activity Log */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Log Aktivitas Terbaru
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 max-h-[300px] overflow-y-auto">
                {activityLogs.map((activity) => (
                  <motion.div
                    key={activity.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="flex items-center gap-3 p-3 rounded-lg bg-slate-50 dark:bg-slate-800"
                  >
                    <div className="flex-shrink-0">
                      {getActivityIcon(activity.type)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <p className="text-sm font-medium text-slate-900 dark:text-white truncate">
                          {activity.message}
                        </p>
                        <Badge className={getActivityColor(activity.type)}>
                          {activity.type}
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <p className="text-xs text-slate-500">
                          {activity.timestamp.toLocaleTimeString("id-ID")}
                        </p>
                        {activity.amount && (
                          <p className="text-xs font-medium text-green-600">
                            {formatCurrency(activity.amount)}
                          </p>
                        )}
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
};
