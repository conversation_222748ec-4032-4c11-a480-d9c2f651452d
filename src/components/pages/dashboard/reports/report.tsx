// Advanced Reports Dashboard
"use client";

import { useState, useEffect } from "react";
import type { NextPage } from "next";
import Head from "next/head";
import DashboardLayout from "@/components/layout/dashboardlayout";
import {
  ReportDashboard,
  AdvancedFilters,
  ExportImportTools,
  FinancialAnalytics,
  RealtimeStats,
  DataVisualization,
} from "./components";
import { motion } from "framer-motion";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  BarChart3,
  TrendingUp,
  PieChart,
  Activity,
  FileText,
  Settings,
} from "lucide-react";

interface FilterState {
  dateRange: string;
  startDate?: Date;
  endDate?: Date;
  category?: string;
  supplier?: string;
  customer?: string;
  status?: string;
}

const ReportsPage: NextPage = () => {
  // Advanced state management
  const [activeTab, setActiveTab] = useState<string>("overview");
  const [filters, setFilters] = useState<FilterState>({
    dateRange: "30d",
  });
  const [refreshInterval, setRefreshInterval] = useState<number | null>(null);

  // DISABLED: Auto-refresh functionality that was causing navigation issues
  // This was calling window.location.reload() which interferes with navigation
  // useEffect(() => {
  //   if (refreshInterval) {
  //     const interval = setInterval(() => {
  //       // Trigger data refresh
  //       window.location.reload();
  //     }, refreshInterval * 1000);

  //     return () => clearInterval(interval);
  //   }
  // }, [refreshInterval]);

  // Handle filter changes
  const handleFilterChange = (newFilters: Partial<FilterState>) => {
    setFilters((prev) => ({ ...prev, ...newFilters }));
  };

  return (
    <DashboardLayout>
      <Head>
        <title>Laporan Keuangan - Kasir Online</title>
      </Head>

      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
        {/* Header Section */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white dark:bg-slate-800 shadow-sm border-b border-slate-200 dark:border-slate-700"
        >
          <div className="w-full mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
                  <BarChart3 className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-slate-900 dark:text-white">
                    Laporan Keuangan
                  </h1>
                  <p className="text-slate-600 dark:text-slate-400">
                    Analisis mendalam untuk bisnis Anda
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <ExportImportTools filters={filters} />
                <AdvancedFilters
                  filters={filters}
                  onFilterChange={handleFilterChange}
                  onRefreshIntervalChange={setRefreshInterval}
                />
              </div>
            </div>
          </div>
        </motion.div>

        {/* Main Content */}
        <div className="w-full mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full"
          >
            <TabsList className="grid w-full grid-cols-4 mb-8 bg-white dark:bg-slate-800 shadow-sm">
              <TabsTrigger
                value="overview"
                className="flex items-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-purple-600 data-[state=active]:text-white"
              >
                <BarChart3 className="h-4 w-4" />
                Overview
              </TabsTrigger>
              <TabsTrigger
                value="analytics"
                className="flex items-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-purple-600 data-[state=active]:text-white"
              >
                <TrendingUp className="h-4 w-4" />
                Analytics
              </TabsTrigger>
              <TabsTrigger
                value="financial"
                className="flex items-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-purple-600 data-[state=active]:text-white"
              >
                <PieChart className="h-4 w-4" />
                Financial
              </TabsTrigger>
              <TabsTrigger
                value="realtime"
                className="flex items-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-purple-600 data-[state=active]:text-white"
              >
                <Activity className="h-4 w-4" />
                Real-time
              </TabsTrigger>
            </TabsList>

            {/* Overview Tab */}
            <TabsContent value="overview" className="space-y-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
              >
                <ReportDashboard filters={filters} />
              </motion.div>
            </TabsContent>

            {/* Analytics Tab */}
            <TabsContent value="analytics" className="space-y-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
              >
                <DataVisualization filters={filters} />
              </motion.div>
            </TabsContent>

            {/* Financial Tab */}
            <TabsContent value="financial" className="space-y-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
              >
                <FinancialAnalytics filters={filters} />
              </motion.div>
            </TabsContent>

            {/* Real-time Tab */}
            <TabsContent value="realtime" className="space-y-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
              >
                <RealtimeStats
                  filters={filters}
                  refreshInterval={refreshInterval}
                />
              </motion.div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default ReportsPage;
