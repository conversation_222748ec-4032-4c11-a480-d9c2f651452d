"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import {
  HelpCircle,
  Mail,
  MessageSquare,
  Phone,
  FileText,
  ExternalLink,
  ChevronDown,
  ChevronRight,
} from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { toast } from "sonner";

export default function SupportSettings() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [activeTab, setActiveTab] = useState("contact");
  const [subject, setSubject] = useState("");
  const [message, setMessage] = useState("");
  const [category, setCategory] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Handle URL parameters for tabs
  useEffect(() => {
    const tab = searchParams.get("tab");
    if (tab && ["contact", "faq", "resources"].includes(tab)) {
      setActiveTab(tab);
    }
  }, [searchParams]);

  const handleSubmitTicket = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    if (!category || !subject || !message) {
      toast.error("Mohon lengkapi semua field yang wajib diisi!");
      return;
    }

    setIsSubmitting(true);

    // Simulate API call with more realistic behavior
    setTimeout(() => {
      toast.success(
        "Tiket dukungan berhasil dikirim! Tim kami akan merespons dalam 24 jam."
      );
      setSubject("");
      setMessage("");
      setCategory("");
      setIsSubmitting(false);
    }, 1500);
  };

  const handleLiveChatClick = () => {
    router.push("/");
  };

  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
    const url = new URL(window.location.href);
    url.searchParams.set("tab", tab);
    router.push(url.pathname + url.search, { scroll: false });
  };

  return (
    <div>
      <div className="px-6 py-5 border-b border-gray-100 dark:border-gray-700 flex items-center gap-4 bg-gradient-to-r from-gray-50 to-white dark:from-gray-800 dark:to-gray-750">
        <HelpCircle className="h-7 w-7 text-indigo-600 dark:text-indigo-400" />
        <div>
          <h2 className="text-xl font-bold text-gray-900 dark:text-gray-100">
            Pusat Dukungan
          </h2>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            Dapatkan bantuan dan dukungan untuk aplikasi Kasir Online
          </p>
        </div>
      </div>

      <div className="p-6 space-y-8">
        <Tabs
          value={activeTab}
          onValueChange={handleTabChange}
          className="w-full"
        >
          <TabsList className="grid grid-cols-3 mb-6">
            <TabsTrigger value="contact" className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              <span>Hubungi Kami</span>
            </TabsTrigger>
            <TabsTrigger value="faq" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              <span>FAQ</span>
            </TabsTrigger>
            <TabsTrigger value="resources" className="flex items-center gap-2">
              <ExternalLink className="h-4 w-4" />
              <span>Sumber Daya</span>
            </TabsTrigger>
          </TabsList>

          {/* Contact Tab */}
          <TabsContent value="contact" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Contact Methods */}
              <div className="md:col-span-1 space-y-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">Metode Kontak</CardTitle>
                    <CardDescription>
                      Pilih cara untuk menghubungi kami
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center gap-3 p-3 border rounded-md">
                      <Mail className="h-5 w-5 text-indigo-600" />
                      <div>
                        <p className="font-medium">Email</p>
                        <p className="text-sm text-muted-foreground">
                          <EMAIL>
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center gap-3 p-3 border rounded-md">
                      <Phone className="h-5 w-5 text-indigo-600" />
                      <div>
                        <p className="font-medium">Telepon</p>
                        <p className="text-sm text-muted-foreground">
                          085725300663
                        </p>
                        <p className="text-xs text-muted-foreground">
                          Senin - Jumat, 09:00 - 17:00 WIB
                        </p>
                      </div>
                    </div>

                    <div
                      className="flex items-center gap-3 p-3 border rounded-md cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                      onClick={handleLiveChatClick}
                    >
                      <MessageSquare className="h-5 w-5 text-indigo-600" />
                      <div>
                        <p className="font-medium">Live Chat</p>
                        <p className="text-sm text-muted-foreground">
                          Tersedia 24/7 (Klik untuk mengakses)
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Support Ticket Form */}
              <div className="md:col-span-2">
                <Card className="border-2 border-dashed border-gray-200 dark:border-gray-700 hover:border-indigo-300 dark:hover:border-indigo-600 transition-colors">
                  <CardHeader className="pb-4">
                    <CardTitle className="flex items-center gap-2">
                      <FileText className="h-5 w-5 text-indigo-600" />
                      Kirim Tiket Dukungan
                    </CardTitle>
                    <CardDescription>
                      Jelaskan masalah Anda secara detail dan tim dukungan kami
                      akan membantu Anda dalam waktu 24 jam
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <form onSubmit={handleSubmitTicket} className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label
                            htmlFor="category"
                            className="text-sm font-medium"
                          >
                            Kategori <span className="text-red-500">*</span>
                          </Label>
                          <Select
                            value={category}
                            onValueChange={setCategory}
                            required
                          >
                            <SelectTrigger
                              id="category"
                              className={`${!category ? "border-red-200" : "border-gray-200"} focus:border-indigo-500`}
                            >
                              <SelectValue placeholder="Pilih kategori masalah" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="account">
                                🔐 Akun & Profil
                              </SelectItem>
                              <SelectItem value="billing">
                                💳 Tagihan & Pembayaran
                              </SelectItem>
                              <SelectItem value="technical">
                                🔧 Masalah Teknis
                              </SelectItem>
                              <SelectItem value="feature">
                                ✨ Permintaan Fitur
                              </SelectItem>
                              <SelectItem value="other">📝 Lainnya</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="space-y-2">
                          <Label
                            htmlFor="priority"
                            className="text-sm font-medium"
                          >
                            Prioritas
                          </Label>
                          <Select defaultValue="medium">
                            <SelectTrigger
                              id="priority"
                              className="focus:border-indigo-500"
                            >
                              <SelectValue placeholder="Pilih prioritas" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="low">🟢 Rendah</SelectItem>
                              <SelectItem value="medium">🟡 Sedang</SelectItem>
                              <SelectItem value="high">🔴 Tinggi</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label
                          htmlFor="subject"
                          className="text-sm font-medium"
                        >
                          Subjek <span className="text-red-500">*</span>
                        </Label>
                        <Input
                          id="subject"
                          placeholder="Ringkasan singkat masalah Anda"
                          value={subject}
                          onChange={(e) => setSubject(e.target.value)}
                          className={`${!subject ? "border-red-200" : "border-gray-200"} focus:border-indigo-500`}
                          required
                        />
                      </div>

                      <div className="space-y-2">
                        <Label
                          htmlFor="message"
                          className="text-sm font-medium"
                        >
                          Deskripsi Masalah{" "}
                          <span className="text-red-500">*</span>
                        </Label>
                        <Textarea
                          id="message"
                          placeholder="Jelaskan masalah Anda secara detail. Sertakan langkah-langkah yang sudah Anda coba dan informasi lain yang relevan..."
                          rows={6}
                          value={message}
                          onChange={(e) => setMessage(e.target.value)}
                          className={`${!message ? "border-red-200" : "border-gray-200"} focus:border-indigo-500 resize-none`}
                          required
                        />
                        <p className="text-xs text-muted-foreground">
                          Minimum 20 karakter ({message.length}/20)
                        </p>
                      </div>

                      <div className="bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                        <div className="flex items-start gap-3">
                          <div className="flex-shrink-0">
                            <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                              <HelpCircle className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                            </div>
                          </div>
                          <div className="flex-1">
                            <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100">
                              Tips untuk mendapatkan bantuan yang lebih cepat:
                            </h4>
                            <ul className="mt-2 text-xs text-blue-700 dark:text-blue-300 space-y-1">
                              <li>• Sertakan screenshot jika memungkinkan</li>
                              <li>
                                • Jelaskan langkah-langkah yang menyebabkan
                                masalah
                              </li>
                              <li>
                                • Sebutkan browser dan perangkat yang Anda
                                gunakan
                              </li>
                            </ul>
                          </div>
                        </div>
                      </div>

                      <Button
                        type="submit"
                        className="w-full bg-indigo-600 hover:bg-indigo-700 text-white py-3 text-base font-medium"
                        disabled={
                          isSubmitting ||
                          !category ||
                          !subject ||
                          message.length < 20
                        }
                      >
                        {isSubmitting ? (
                          <div className="flex items-center gap-2">
                            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                            Mengirim Tiket...
                          </div>
                        ) : (
                          <div className="flex items-center gap-2">
                            <Mail className="h-4 w-4" />
                            Kirim Tiket Dukungan
                          </div>
                        )}
                      </Button>
                    </form>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          {/* FAQ Tab */}
          <TabsContent value="faq" className="space-y-6">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Pertanyaan Umum</CardTitle>
                <CardDescription>
                  Jawaban untuk pertanyaan yang sering diajukan
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Accordion type="single" collapsible className="w-full">
                  <AccordionItem value="item-1">
                    <AccordionTrigger className="text-left">
                      Bagaimana cara mengubah paket langganan?
                    </AccordionTrigger>
                    <AccordionContent>
                      Anda dapat mengubah paket langganan melalui halaman
                      Pengaturan &gt; Billing. Pilih paket yang diinginkan dan
                      ikuti petunjuk pembayaran.
                    </AccordionContent>
                  </AccordionItem>

                  <AccordionItem value="item-2">
                    <AccordionTrigger className="text-left">
                      Bagaimana cara menambahkan karyawan?
                    </AccordionTrigger>
                    <AccordionContent>
                      Anda dapat menambahkan karyawan melalui halaman Pengaturan
                      &gt; Karyawan. Klik tombol &quot;Tambah Karyawan&quot; dan
                      isi informasi yang diperlukan.
                    </AccordionContent>
                  </AccordionItem>

                  <AccordionItem value="item-3">
                    <AccordionTrigger className="text-left">
                      Apakah saya bisa menggunakan aplikasi ini secara offline?
                    </AccordionTrigger>
                    <AccordionContent>
                      Ya, aplikasi Kasir Online memiliki fitur offline mode yang
                      memungkinkan Anda tetap menggunakan aplikasi meskipun
                      tidak ada koneksi internet. Data akan disinkronkan saat
                      koneksi internet tersedia kembali.
                    </AccordionContent>
                  </AccordionItem>

                  <AccordionItem value="item-4">
                    <AccordionTrigger className="text-left">
                      Bagaimana cara mengekspor laporan penjualan?
                    </AccordionTrigger>
                    <AccordionContent>
                      Anda dapat mengekspor laporan penjualan melalui halaman
                      Laporan. Pilih periode laporan yang diinginkan, kemudian
                      klik tombol &quot;Ekspor&quot; dan pilih format yang
                      diinginkan (PDF, Excel, atau CSV).
                    </AccordionContent>
                  </AccordionItem>

                  <AccordionItem value="item-5">
                    <AccordionTrigger className="text-left">
                      Apakah aplikasi ini mendukung printer thermal?
                    </AccordionTrigger>
                    <AccordionContent>
                      Ya, aplikasi Kasir Online mendukung berbagai jenis printer
                      thermal yang umum digunakan untuk mencetak struk. Anda
                      dapat mengatur printer melalui halaman Pengaturan &gt;
                      Perangkat.
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
              </CardContent>
              <CardFooter>
                <Button variant="outline" className="w-full">
                  Lihat Semua FAQ
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          {/* Resources Tab */}
          <TabsContent value="resources" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle>Panduan Pengguna</CardTitle>
                  <CardDescription>
                    Pelajari cara menggunakan aplikasi Kasir Online
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4 text-indigo-600" />
                      <p className="font-medium">Panduan Memulai</p>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Panduan langkah demi langkah untuk memulai menggunakan
                      aplikasi Kasir Online.
                    </p>
                    <Button variant="link" className="h-auto p-0">
                      Baca Panduan
                    </Button>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4 text-indigo-600" />
                      <p className="font-medium">Panduan Fitur Lanjutan</p>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Pelajari fitur-fitur lanjutan untuk mengoptimalkan
                      penggunaan aplikasi.
                    </p>
                    <Button variant="link" className="h-auto p-0">
                      Baca Panduan
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle>Video Tutorial</CardTitle>
                  <CardDescription>
                    Pelajari melalui video tutorial
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <ExternalLink className="h-4 w-4 text-indigo-600" />
                      <p className="font-medium">Pengenalan Aplikasi</p>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Video pengenalan aplikasi Kasir Online dan fitur-fiturnya.
                    </p>
                    <Button variant="link" className="h-auto p-0">
                      Tonton Video
                    </Button>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <ExternalLink className="h-4 w-4 text-indigo-600" />
                      <p className="font-medium">Tutorial Transaksi</p>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Pelajari cara melakukan transaksi penjualan dan pembelian.
                    </p>
                    <Button variant="link" className="h-auto p-0">
                      Tonton Video
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Artikel & Blog</CardTitle>
                <CardDescription>
                  Artikel terbaru tentang tips dan trik menggunakan aplikasi
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="border-b pb-4">
                    <h3 className="font-medium">
                      5 Cara Meningkatkan Efisiensi Kasir
                    </h3>
                    <p className="text-sm text-muted-foreground mt-1">
                      Pelajari cara meningkatkan efisiensi operasional kasir
                      Anda dengan aplikasi KivaPOS.
                    </p>
                    <div className="flex items-center gap-2 mt-2">
                      <Button variant="link" className="h-auto p-0">
                        Baca Artikel
                      </Button>
                      <p className="text-xs text-muted-foreground">
                        5 menit membaca
                      </p>
                    </div>
                  </div>

                  <div className="border-b pb-4">
                    <h3 className="font-medium">
                      Cara Menganalisis Laporan Penjualan
                    </h3>
                    <p className="text-sm text-muted-foreground mt-1">
                      Panduan lengkap untuk menganalisis laporan penjualan dan
                      mengambil keputusan bisnis.
                    </p>
                    <div className="flex items-center gap-2 mt-2">
                      <Button variant="link" className="h-auto p-0">
                        Baca Artikel
                      </Button>
                      <p className="text-xs text-muted-foreground">
                        7 menit membaca
                      </p>
                    </div>
                  </div>

                  <div>
                    <h3 className="font-medium">
                      Tips Mengelola Inventaris dengan Efektif
                    </h3>
                    <p className="text-sm text-muted-foreground mt-1">
                      Pelajari cara mengelola inventaris dengan efektif
                      menggunakan fitur manajemen stok.
                    </p>
                    <div className="flex items-center gap-2 mt-2">
                      <Button variant="link" className="h-auto p-0">
                        Baca Artikel
                      </Button>
                      <p className="text-xs text-muted-foreground">
                        6 menit membaca
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button variant="outline" className="w-full">
                  Lihat Semua Artikel
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
