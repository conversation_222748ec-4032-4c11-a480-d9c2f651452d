"use client";

import { useState, useEffect } from "react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import { cn } from "@/lib/utils";
import { Role } from "@prisma/client";
import { PermissionCheck } from "@/components/auth/permission-check";
import {
  UserCircle,
  Users,
  Palette,
  Bell,
  CreditCard,
  Receipt,
  ShieldCheck,
  HelpCircle,
  ChevronDown,
  ChevronRight,
  ExternalLink,
} from "lucide-react";

interface SettingsLayoutProps {
  children: React.ReactNode;
}

interface SettingsNavItem {
  title: string;
  href: string;
  icon: React.ElementType;
  roles?: Role[];
}

// Define the navigation items based on the image
const settingsNavItems: SettingsNavItem[] = [
  {
    title: "Profile",
    href: "/dashboard/settings/profile",
    icon: UserCircle,
  },
  {
    title: "Tampilan",
    href: "/dashboard/settings/appearance",
    icon: Pa<PERSON>,
  },
  {
    title: "Notif<PERSON><PERSON>",
    href: "/dashboard/settings/notifications",
    icon: Bell,
  },
  {
    title: "Billing",
    href: "/dashboard/settings/billing",
    icon: CreditCard,
  },
  {
    title: "Plan & Tagihan",
    href: "/dashboard/settings/plans",
    icon: Receipt,
  },
  {
    title: "Redirect Settings",
    href: "/dashboard/settings/redirections",
    icon: ExternalLink,
  },
  {
    title: "Keamanan",
    href: "/dashboard/settings/security",
    icon: ShieldCheck,
  },
  {
    title: "Support",
    href: "/dashboard/settings/support",
    icon: HelpCircle,
  },
];

export default function SettingsLayout({ children }: SettingsLayoutProps) {
  const pathname = usePathname();
  const [activeItem, setActiveItem] = useState("");

  // Set active item based on current pathname
  useEffect(() => {
    const matchingItem = settingsNavItems.find((item) =>
      pathname.includes(item.href)
    );

    if (matchingItem) {
      setActiveItem(matchingItem.href);
    } else if (pathname === "/dashboard/settings") {
      // Default to profile settings if at the base path
      setActiveItem("/dashboard/settings/profile");
    }
  }, [pathname]);

  return (
    <div className="container mx-auto py-6 h-full">
      <h1 className="text-2xl font-bold mb-6">Pengaturan</h1>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 h-full">
        {/* Settings Navigation - Left Side */}
        <div className="lg:col-span-1">
          <div className="sticky top-6 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden max-h-[calc(100vh-8rem)]">
            <nav className="flex flex-col overflow-y-auto">
              {settingsNavItems.map((item) => {
                // If the item has roles restriction, wrap it in PermissionCheck
                if (item.roles) {
                  return (
                    <PermissionCheck key={item.href} requiredRoles={item.roles}>
                      <NavItem
                        item={item}
                        isActive={activeItem === item.href}
                        onClick={() => setActiveItem(item.href)}
                      />
                    </PermissionCheck>
                  );
                }

                // Otherwise, render the nav item directly
                return (
                  <NavItem
                    key={item.href}
                    item={item}
                    isActive={activeItem === item.href}
                    onClick={() => setActiveItem(item.href)}
                  />
                );
              })}
            </nav>
          </div>
        </div>

        {/* Main Content - Right Side */}
        <div className="lg:col-span-3">
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden h-full">
            <div className="h-full overflow-y-auto">{children}</div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Navigation Item Component
function NavItem({
  item,
  isActive,
  onClick,
}: {
  item: SettingsNavItem;
  isActive: boolean;
  onClick: () => void;
}) {
  return (
    <Link
      href={item.href}
      className={cn(
        "flex items-start gap-3 px-4 py-3 border-b border-gray-100 dark:border-gray-700 last:border-0 transition-colors",
        isActive
          ? "bg-gray-100 dark:bg-gray-700 text-primary dark:text-primary-foreground"
          : "hover:bg-gray-50 dark:hover:bg-gray-700/50"
      )}
      onClick={onClick}
    >
      <item.icon
        className={cn(
          "h-5 w-5 mt-0.5 flex-shrink-0",
          isActive
            ? "text-primary dark:text-primary-foreground"
            : "text-gray-500 dark:text-gray-400"
        )}
      />
      <div className="flex-grow">
        <span className="font-medium">{item.title}</span>
      </div>
      {isActive ? (
        <ChevronDown className="ml-auto h-4 w-4 flex-shrink-0" />
      ) : (
        <ChevronRight className="ml-auto h-4 w-4 text-gray-400 flex-shrink-0" />
      )}
    </Link>
  );
}
