"use client";

import React, { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { DatePicker } from "@/components/ui/date-picker";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Progress } from "@/components/ui/progress";
import {
  Download,
  Upload,
  FileSpreadsheet,
  AlertCircle,
  CheckCircle,
  Info,
  Calendar,
} from "lucide-react";
import * as XLSX from "xlsx-js-style";
import { toast } from "sonner";
import { getSupplierReportData } from "@/actions/reports/reports";
import {
  generateDateStringForFilename,
  validateExportPeriod,
} from "@/utils/dateUtils";

interface ImportSummary {
  suppliersCreated: number;
  errors: string[];
}

interface ExportConfig {
  reportType: "harian" | "bulanan" | "tahunan";
  selectedDate: Date;
  selectedMonth: number;
  selectedYear: number;
  includeSummary: boolean;
  includeCharts: boolean;
}

interface SupplierImportExportProps {
  onRefresh?: () => void; // Callback to refresh the suppliers list after import
}

// Function to create supplier-only Excel report
const createSupplierOnlyExcelReport = (
  suppliers: any[],
  options: {
    reportTitle: string;
    includeSummary: boolean;
    totalSuppliers: number;
  }
) => {
  const workbook = XLSX.utils.book_new();

  // Create header info
  const headerData = [
    [options.reportTitle],
    [`Diekspor pada: ${new Date().toLocaleString("id-ID")}`],
    [`Total Supplier: ${options.totalSuppliers}`],
    [], // Empty row
  ];

  // Define column headers
  const columnHeaders = [
    "ID Supplier",
    "Nama Supplier",
    "Nama Depan",
    "Nama Tengah",
    "Nama Belakang",
    "Nama Kontak",
    "Telepon",
    "Email",
    "Jenis Identitas",
    "Nomor Identitas",
    "NIK",
    "NPWP",
    "Nama Perusahaan",
    "Alamat",
    "Alamat Penagihan",
    "Alamat Pengiriman",
    "Bank",
    "Cabang Bank",
    "Nama Pemegang Rekening",
    "Nomor Rekening",
    "Catatan",
  ];

  // Prepare supplier data
  const supplierData = suppliers.map((supplier) => {
    return [
      supplier.id || "-",
      supplier.name || "-",
      supplier.firstName || "-",
      supplier.middleName || "-",
      supplier.lastName || "-",
      supplier.contactName || "-",
      supplier.phone || "-",
      supplier.email || "-",
      supplier.identityType || "-",
      supplier.identityNumber || "-",
      supplier.NIK || "-",
      supplier.NPWP || "-",
      supplier.companyName || "-",
      supplier.address || "-",
      supplier.billingAddress || "-",
      supplier.shippingAddress || "-",
      supplier.bankName || "-",
      supplier.bankBranch || "-",
      supplier.accountHolder || "-",
      supplier.accountNumber || "-",
      supplier.notes || "-",
    ];
  });

  // Combine all data
  const worksheetData = [...headerData, columnHeaders, ...supplierData];

  // Create worksheet
  const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);

  // Set column widths
  const columnWidths = [
    { wch: 25 }, // ID Supplier
    { wch: 30 }, // Nama Supplier
    { wch: 20 }, // Nama Depan
    { wch: 20 }, // Nama Tengah
    { wch: 20 }, // Nama Belakang
    { wch: 25 }, // Nama Kontak
    { wch: 15 }, // Telepon
    { wch: 30 }, // Email
    { wch: 15 }, // Jenis Identitas
    { wch: 20 }, // Nomor Identitas
    { wch: 20 }, // NIK
    { wch: 20 }, // NPWP
    { wch: 30 }, // Nama Perusahaan
    { wch: 40 }, // Alamat
    { wch: 40 }, // Alamat Penagihan
    { wch: 40 }, // Alamat Pengiriman
    { wch: 20 }, // Bank
    { wch: 20 }, // Cabang Bank
    { wch: 25 }, // Nama Pemegang Rekening
    { wch: 20 }, // Nomor Rekening
    { wch: 50 }, // Catatan
  ];
  worksheet["!cols"] = columnWidths;

  // Style the header rows
  const headerStyle = {
    font: { bold: true, sz: 14 },
    alignment: { horizontal: "center" },
    fill: { fgColor: { rgb: "E3F2FD" } },
  };

  const columnHeaderStyle = {
    font: { bold: true, sz: 12 },
    alignment: { horizontal: "center" },
    fill: { fgColor: { rgb: "BBDEFB" } },
    border: {
      top: { style: "thin" },
      bottom: { style: "thin" },
      left: { style: "thin" },
      right: { style: "thin" },
    },
  };

  // Apply styles to header
  if (worksheet["A1"]) worksheet["A1"].s = headerStyle;
  if (worksheet["A2"]) worksheet["A2"].s = { font: { sz: 10 } };
  if (worksheet["A3"]) worksheet["A3"].s = { font: { sz: 10 } };

  // Apply styles to column headers (row 5)
  const headerRowIndex = 5;
  columnHeaders.forEach((_, colIndex) => {
    const cellAddress = XLSX.utils.encode_cell({
      r: headerRowIndex - 1,
      c: colIndex,
    });
    if (worksheet[cellAddress]) {
      worksheet[cellAddress].s = columnHeaderStyle;
    }
  });

  // Add worksheet to workbook
  XLSX.utils.book_append_sheet(workbook, worksheet, "Data Supplier");

  return workbook;
};

export const SupplierImportExport: React.FC<SupplierImportExportProps> = ({
  onRefresh,
}) => {
  // Import states
  const [showImportDialog, setShowImportDialog] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [importProgress, setImportProgress] = useState(0);
  const [importSummary, setImportSummary] = useState<ImportSummary | null>(
    null
  );
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Export states
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [exportConfig, setExportConfig] = useState<ExportConfig>({
    reportType: "bulanan",
    selectedDate: new Date(),
    selectedMonth: new Date().getMonth(),
    selectedYear: new Date().getFullYear(),
    includeSummary: false,
    includeCharts: false,
  });

  // Download template function
  const downloadTemplate = () => {
    try {
      // Create a simple template for suppliers
      const workbook = XLSX.utils.book_new();

      // Template headers
      const headers = [
        "Nama Supplier*",
        "Nama Depan",
        "Nama Tengah",
        "Nama Belakang",
        "Nama Kontak",
        "Telepon",
        "Email",
        "Jenis Identitas",
        "Nomor Identitas",
        "NIK",
        "NPWP",
        "Nama Perusahaan",
        "Alamat",
        "Alamat Penagihan",
        "Alamat Pengiriman",
        "Bank",
        "Cabang Bank",
        "Nama Pemegang Rekening",
        "Nomor Rekening",
        "Catatan",
      ];

      // Create worksheet with headers
      const worksheet = XLSX.utils.aoa_to_sheet([headers]);

      // Add worksheet to workbook
      XLSX.utils.book_append_sheet(workbook, worksheet, "Template Supplier");

      const fileName = `template-import-supplier-${new Date().toISOString().split("T")[0]}.xlsx`;
      XLSX.writeFile(workbook, fileName);
      toast.success("Template berhasil diunduh!");
    } catch (error) {
      console.error("Template download error:", error);
      toast.error("Gagal mengunduh template");
    }
  };

  // Advanced export handler
  const handleAdvancedExport = async () => {
    try {
      setIsExporting(true);
      setExportProgress(0);

      // Validate export period
      const validationError = validateExportPeriod(exportConfig.reportType, {
        selectedDate: exportConfig.selectedDate,
        selectedMonth: exportConfig.selectedMonth,
        selectedYear: exportConfig.selectedYear,
      });

      if (validationError) {
        toast.error(validationError);
        return;
      }

      // Determine period label
      let periodLabel: string = "";
      if (exportConfig.reportType === "harian") {
        periodLabel = `Harian - ${exportConfig.selectedDate.toLocaleDateString("id-ID")}`;
      } else if (exportConfig.reportType === "bulanan") {
        const monthNames = [
          "Januari",
          "Februari",
          "Maret",
          "April",
          "Mei",
          "Juni",
          "Juli",
          "Agustus",
          "September",
          "Oktober",
          "November",
          "Desember",
        ];
        periodLabel = `Bulanan - ${monthNames[exportConfig.selectedMonth]} ${exportConfig.selectedYear}`;
      } else if (exportConfig.reportType === "tahunan") {
        periodLabel = `Tahunan - ${exportConfig.selectedYear}`;
      }

      setExportProgress(40);

      // Fetch supplier data
      const supplierResult = await getSupplierReportData();

      setExportProgress(70);

      if (supplierResult.error) {
        console.error("Supplier data fetch error:", supplierResult.error);
        throw new Error(supplierResult.error);
      }

      // Check if we have data
      if (!supplierResult.data || supplierResult.data.length === 0) {
        toast.error(
          "Tidak ada data supplier untuk diekspor pada periode yang dipilih."
        );
        return;
      }

      console.log(
        "Supplier data fetched:",
        supplierResult.data?.length || 0,
        "suppliers"
      );

      const reportData = {
        suppliers: supplierResult.data,
        summary: {
          totalSuppliers: supplierResult.data.length,
          period: periodLabel,
          generatedAt: new Date(),
        },
      };

      setExportProgress(85);

      // Generate Excel export - Suppliers only
      const workbook = createSupplierOnlyExcelReport(reportData.suppliers, {
        reportTitle: `Data Supplier - ${periodLabel}`,
        includeSummary: exportConfig.includeSummary,
        totalSuppliers: reportData.suppliers.length,
      });

      setExportProgress(100);

      // Generate filename based on selected date range
      const dateString = generateDateStringForFilename(
        exportConfig.reportType,
        {
          selectedDate: exportConfig.selectedDate,
          selectedMonth: exportConfig.selectedMonth,
          selectedYear: exportConfig.selectedYear,
        }
      );

      const fileName = `data-supplier-${exportConfig.reportType}-${dateString}.xlsx`;
      XLSX.writeFile(workbook, fileName);

      toast.success("Data supplier berhasil diekspor!");
      setShowExportDialog(false);
    } catch (error) {
      console.error("Export error:", error);
      toast.error("Gagal mengekspor data supplier");
    } finally {
      setIsExporting(false);
      setExportProgress(0);
    }
  };

  // Handle import function
  const handleImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      toast.error("Ukuran file maksimal 10MB");
      return;
    }

    // Validate file type
    if (!file.name.match(/\.(xlsx|xls)$/i)) {
      toast.error("Format file harus Excel (.xlsx atau .xls)");
      return;
    }

    setIsImporting(true);
    setImportProgress(0);
    setImportSummary(null);

    try {
      setImportProgress(20);

      // Read file for future implementation
      await file.arrayBuffer();
      setImportProgress(40);

      // TODO: Implement supplier import API call
      // const result = await importSuppliers(arrayBuffer);
      setImportProgress(80);

      // Simulate success for now
      setImportProgress(100);
      setImportSummary({
        suppliersCreated: 0,
        errors: ["Import functionality will be implemented soon"],
      });
      toast.info("Import functionality will be implemented soon");

      // Auto-refresh data on successful import
      if (onRefresh) {
        setTimeout(() => {
          onRefresh();
        }, 1500);
      }
    } catch (error) {
      console.error("Import error:", error);
      toast.error("Gagal mengimpor file");
    } finally {
      setIsImporting(false);
      setImportProgress(0);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  return (
    <div className="flex items-center gap-2">
      {/* Import Button and Dialog */}
      <Dialog open={showImportDialog} onOpenChange={setShowImportDialog}>
        <DialogTrigger asChild>
          <Button
            variant="outline"
            className="flex items-center gap-2 cursor-pointer"
          >
            <Upload className="h-4 w-4" />
            Import
          </Button>
        </DialogTrigger>
        <DialogContent className="max-w-2xl max-h-[80vh] flex flex-col rounded-md">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle className="flex items-center gap-2">
              <Upload className="h-5 w-5" />
              Import Data Supplier
            </DialogTitle>
            <DialogDescription className="flex text-left">
              Import data supplier dari file Excel dengan mudah dan aman
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-y-auto space-y-4 p-2">
            {/* Instructions */}
            <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-lg">
              <div className="flex items-start gap-3">
                <Info className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                <div className="space-y-2">
                  <h4 className="font-medium text-blue-900 dark:text-blue-100">
                    Cara Import Data Supplier:
                  </h4>
                  <ol className="text-sm text-blue-800 dark:text-blue-200 space-y-1 list-decimal list-inside">
                    <li>
                      Download template Excel dengan klik tombol &quot;Download
                      Template&quot;
                    </li>
                    <li>Isi data supplier sesuai format yang tersedia</li>
                    <li>Upload file Excel yang sudah diisi</li>
                    <li>Tunggu proses import selesai</li>
                  </ol>
                </div>
              </div>
            </div>

            {/* Download Template */}
            <div className="space-y-2">
              <h4 className="font-medium">1. Download Template</h4>
              <Button
                onClick={downloadTemplate}
                variant="outline"
                className="w-full flex items-center gap-2 cursor-pointer"
              >
                <FileSpreadsheet className="h-4 w-4" />
                Download Template Excel
              </Button>
            </div>

            <Separator />

            {/* File Upload */}
            <div className="space-y-2">
              <h4 className="font-medium">2. Upload File Excel</h4>
              <div className="border-2 border-dashed border-slate-300 rounded-lg p-6 text-center">
                <Upload className="h-12 w-12 mx-auto text-slate-400 mb-4" />
                <p className="text-sm text-slate-600 mb-2">
                  Klik untuk memilih file atau drag & drop
                </p>
                <p className="text-xs text-slate-500">
                  Format: .xlsx, .xls (Maksimal 10MB)
                </p>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".xlsx,.xls"
                  onChange={handleImport}
                  className="hidden"
                />
                <Button
                  variant="outline"
                  onClick={() => fileInputRef.current?.click()}
                  className="mt-4 cursor-pointer"
                  disabled={isImporting}
                >
                  {isImporting ? "Memproses..." : "Pilih File Excel"}
                </Button>
              </div>
            </div>

            {/* Import Progress */}
            {isImporting && (
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Mengimpor data supplier...</span>
                  <span>{importProgress}%</span>
                </div>
                <Progress value={importProgress} />
              </div>
            )}

            {/* Import Summary */}
            {importSummary && (
              <div className="bg-slate-50 dark:bg-slate-800 p-4 rounded-lg">
                <h4 className="font-medium mb-2 flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  Hasil Import:
                </h4>
                <div className="text-sm space-y-1">
                  <p>
                    ✅ Supplier berhasil dibuat:{" "}
                    {importSummary.suppliersCreated}
                  </p>

                  {importSummary.errors && importSummary.errors.length > 0 && (
                    <div className="mt-3">
                      <p className="text-red-600 font-medium flex items-center gap-1">
                        <AlertCircle className="h-4 w-4" />
                        Error:
                      </p>
                      <div className="max-h-32 overflow-y-auto">
                        {importSummary.errors
                          .slice(0, 10)
                          .map((error: string, index: number) => (
                            <p key={index} className="text-xs text-red-600">
                              • {error}
                            </p>
                          ))}
                        {importSummary.errors.length > 10 && (
                          <p className="text-xs text-red-600">
                            ... dan {importSummary.errors.length - 10} error
                            lainnya
                          </p>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Close Button */}
            {importSummary && (
              <div className="flex justify-end pt-4">
                <Button
                  onClick={() => {
                    setShowImportDialog(false);
                    setImportSummary(null);
                    if (fileInputRef.current) {
                      fileInputRef.current.value = "";
                    }
                  }}
                  className="flex items-center gap-2"
                >
                  Tutup
                </Button>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Advanced Export Dialog */}
      <Dialog open={showExportDialog} onOpenChange={setShowExportDialog}>
        <DialogTrigger asChild>
          <Button
            variant="outline"
            className="flex items-center gap-2 cursor-pointer"
          >
            <Download className="h-4 w-4" />
            Export
          </Button>
        </DialogTrigger>
        <DialogContent className="max-w-2xl max-h-[80vh] flex flex-col rounded-md">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle className="flex items-center gap-2">
              <Download className="h-5 w-5" />
              Export Data Supplier
            </DialogTitle>
            <DialogDescription className="flex text-left">
              Pilih periode dan format untuk mengekspor data supplier
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-y-auto space-y-4 p-2">
            {/* Report Type Selection */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Jenis Laporan</Label>
              <div className="grid grid-cols-3 gap-2">
                {[
                  { value: "harian", label: "Harian", icon: Calendar },
                  { value: "bulanan", label: "Bulanan", icon: Calendar },
                  { value: "tahunan", label: "Tahunan", icon: Calendar },
                ].map((type) => (
                  <Card
                    key={type.value}
                    className={`cursor-pointer transition-all hover:shadow-sm ${
                      exportConfig.reportType === type.value
                        ? "ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-950"
                        : "hover:bg-slate-50 dark:hover:bg-slate-800"
                    }`}
                    onClick={() =>
                      setExportConfig((prev) => ({
                        ...prev,
                        reportType: type.value as any,
                      }))
                    }
                  >
                    <CardContent className="p-3 text-center">
                      <type.icon className="h-5 w-5 mx-auto mb-1 text-blue-500" />
                      <p className="text-xs font-medium">{type.label}</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>

            <Separator />

            {/* Date/Period Selection */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Pilih Periode</Label>

              {exportConfig.reportType === "harian" && (
                <div>
                  <DatePicker
                    date={exportConfig.selectedDate}
                    setDate={(date) => {
                      setExportConfig((prev) => ({
                        ...prev,
                        selectedDate: date || new Date(),
                      }));
                    }}
                    placeholder="Pilih tanggal"
                    className="w-full h-9 cursor-pointer"
                  />
                </div>
              )}

              {exportConfig.reportType === "bulanan" && (
                <div className="grid grid-cols-2 gap-2">
                  <Select
                    value={exportConfig.selectedMonth?.toString() || ""}
                    onValueChange={(value) => {
                      setExportConfig((prev) => ({
                        ...prev,
                        selectedMonth: parseInt(value),
                      }));
                    }}
                  >
                    <SelectTrigger className="h-9 w-full">
                      <SelectValue placeholder="Bulan" />
                    </SelectTrigger>
                    <SelectContent>
                      {[
                        "Januari",
                        "Februari",
                        "Maret",
                        "April",
                        "Mei",
                        "Juni",
                        "Juli",
                        "Agustus",
                        "September",
                        "Oktober",
                        "November",
                        "Desember",
                      ].map((month, index) => (
                        <SelectItem key={index} value={index.toString()}>
                          {month}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Input
                    type="number"
                    min="2020"
                    max="2030"
                    placeholder="Tahun"
                    value={exportConfig.selectedYear || ""}
                    onChange={(e) => {
                      setExportConfig((prev) => ({
                        ...prev,
                        selectedYear:
                          parseInt(e.target.value) || new Date().getFullYear(),
                      }));
                    }}
                    className="h-11"
                  />
                </div>
              )}

              {exportConfig.reportType === "tahunan" && (
                <div>
                  <Input
                    type="number"
                    min="2020"
                    max="2030"
                    placeholder="Tahun"
                    value={exportConfig.selectedYear || ""}
                    onChange={(e) => {
                      setExportConfig((prev) => ({
                        ...prev,
                        selectedYear:
                          parseInt(e.target.value) || new Date().getFullYear(),
                      }));
                    }}
                    className="w-full h-9"
                  />
                </div>
              )}
            </div>

            <Separator />

            {/* Format Selection - Excel Only */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Format Export</Label>
              <div className="flex items-center gap-2 p-3 border rounded-md bg-gray-50 dark:bg-gray-800">
                <FileSpreadsheet className="h-4 w-4 text-green-600" />
                <span className="text-sm font-medium">Excel (.xlsx)</span>
                <span className="text-xs text-gray-500 ml-auto">
                  Dengan sheet &apos;Data Supplier&apos; dan &apos;Info
                  Dokumen&apos;
                </span>
              </div>
            </div>

            <Separator />

            {/* Additional Options */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Opsi Tambahan</Label>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="includeSummary"
                    checked={exportConfig.includeSummary}
                    onCheckedChange={(checked) =>
                      setExportConfig((prev) => ({
                        ...prev,
                        includeSummary: checked as boolean,
                      }))
                    }
                  />
                  <Label
                    htmlFor="includeSummary"
                    className="text-sm cursor-pointer"
                  >
                    Sertakan ringkasan data
                  </Label>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons - Fixed at bottom */}
          <div className="flex-shrink-0 flex justify-between pt-4 border-t bg-white dark:bg-gray-900">
            <Button
              className="cursor-pointer"
              variant="outline"
              onClick={() => setShowExportDialog(false)}
            >
              Batal
            </Button>
            <Button
              onClick={handleAdvancedExport}
              disabled={isExporting}
              className="flex items-center gap-2 cursor-pointer"
            >
              <Download className="h-4 w-4" />
              {isExporting ? "Mengekspor..." : "Export Data"}
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Export Progress Dialog */}
      {isExporting && (
        <Dialog open={isExporting}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Download className="h-5 w-5" />
                Mengekspor Data Supplier
              </DialogTitle>
              <DialogDescription>
                Mohon tunggu, sedang memproses data supplier...
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Memproses data...</span>
                  <span>{exportProgress}%</span>
                </div>
                <Progress value={exportProgress} className="w-full" />
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};
