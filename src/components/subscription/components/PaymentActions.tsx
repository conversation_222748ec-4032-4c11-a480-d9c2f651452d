import React from "react";
import {
  MagnifyingGlassIcon,
  AdjustmentsHorizontalIcon,
} from "@heroicons/react/24/outline";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { RefreshCwIcon } from "lucide-react";
import type { PaymentColumnVisibility } from "../types";
import { paymentHistoryColumnConfig } from "../config/columnConfig";
import { PaymentFilter, PaymentFilterState } from "./PaymentFilter";

interface PaymentActionsProps {
  columnVisibility: PaymentColumnVisibility;
  setColumnVisibility: React.Dispatch<React.SetStateAction<PaymentColumnVisibility>>;
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  filters: PaymentFilterState;
  onFilterChange: (filters: PaymentFilterState) => void;
  onRefresh?: () => void;
  totalPayments: number;
  isLoading?: boolean;
}

export const PaymentActions: React.FC<PaymentActionsProps> = ({
  columnVisibility,
  setColumnVisibility,
  searchTerm,
  setSearchTerm,
  filters,
  onFilterChange,
  onRefresh,
  totalPayments,
  isLoading = false,
}) => {
  return (
    <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 p-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm">
      {/* Left side - Title and Stats */}
      <div className="flex items-center gap-4">
        <div>
          <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Riwayat Pembayaran
          </h2>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {totalPayments} transaksi ditemukan
          </p>
        </div>
        
        {/* Refresh Button */}
        {onRefresh && (
          <Button
            variant="outline"
            size="sm"
            onClick={onRefresh}
            disabled={isLoading}
            className="flex items-center gap-2"
          >
            <RefreshCwIcon className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        )}
      </div>

      {/* Right side - Controls */}
      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 w-full sm:w-auto">
        {/* Column Visibility Dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="flex items-center gap-2">
              <AdjustmentsHorizontalIcon className="h-4 w-4" />
              Kolom
              <Badge variant="secondary" className="ml-1 text-xs">
                {Object.values(columnVisibility).filter(Boolean).length}
              </Badge>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <DropdownMenuLabel>Pilih Kolom</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <div className="max-h-[300px] overflow-y-auto pr-1 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-transparent">
              {paymentHistoryColumnConfig.map((column) => (
                <DropdownMenuCheckboxItem
                  key={column.key}
                  checked={columnVisibility[column.key]}
                  onCheckedChange={(checked) =>
                    setColumnVisibility((prev) => ({
                      ...prev,
                      [column.key]: !!checked,
                    }))
                  }
                  onSelect={(e) => e.preventDefault()}
                >
                  {column.label}
                </DropdownMenuCheckboxItem>
              ))}
            </div>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Filter Component */}
        <PaymentFilter
          filters={filters}
          onFilterChange={onFilterChange}
        />
      </div>

      {/* Search Input - Full width on mobile */}
      <div className="flex items-center gap-2 w-full sm:w-auto">
        <div className="relative flex-grow">
          <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
            <MagnifyingGlassIcon
              className="h-5 w-5 text-gray-400 dark:text-gray-500"
              aria-hidden="true"
            />
          </div>
          <input
            type="text"
            placeholder="Cari pembayaran..."
            className="block w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 py-2 pl-10 pr-3 leading-5 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-indigo-500 dark:focus:border-indigo-400 focus:placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-indigo-500 dark:focus:ring-indigo-400 sm:text-sm"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>
    </div>
  );
};
